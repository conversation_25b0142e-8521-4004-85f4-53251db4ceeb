import MediaModal from "@/Components/MediaModal";
import { Button } from "@/Components/ui/button";
import { Link } from "@inertiajs/react";
import { useState, useEffect } from "react";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@/Components/ui/carousel";
import { usePage } from "@inertiajs/react";
import { Equipment as EquipmentType, Title } from "@/types";

// const equipment = [
//   {
//     title: "Máy Dcarer Navigation",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/v1752938867/M%C3%A1y_Dcarer_Navigation_ndcpts.png",
//     brief: "Xác định được vị trí ống dây thần kinh; hình thái, hướng mọc lệch của răng đem lại hiệu quả, an toàn cho các ca tiểu phẫu nhổ răng khôn.\n\n" +
//       "Chẩn đoán sớm và xác định mức độ nghiêm trọng của các vấn đề về răng...",
//     description: "Xác định được vị trí ống dây thần kinh; hình thái, hướng mọc lệch của răng đem lại hiệu quả, an toàn cho các ca tiểu phẫu nhổ răng khôn.\n\n" +
//       "Chẩn đoán sớm và xác định mức độ nghiêm trọng của các vấn đề về răng, nướu như sâu răng, viêm tủy, viêm nướu,... để đưa ra khuyến cáo và điều trị kịp thời giúp tránh được các biến chứng về sau.\n\n" +
//       "Tích hợp phần mềm lập kế hoạch implant, đồng thời xác định được mật độ xương hàm - một trong những yếu tố tiên quyết đem đến sự thành công trong các ca cấy ghép implant.\n\n" +
//       "Phim cephalo hỗ trợ bác sĩ đánh giá thẩm mỹ và theo dõi trong quá trình.",
//     type: "image",
//   },
//   {
//     title: "Máy Activelink Plasma",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/v1752938862/M%C3%A1y_Activelink_Plasma_lpjfax.jpg",
//     brief: 
//       "Máy scan Medit i700 giúp nâng cao dịch vụ chăm sóc răng miệng, khách hàng không còn cảm giác khó chịu như phương pháp lấy dấu thông thường. Quy trình phục hình trở nên đơn giản và nhanh chóng.\n" +
//       "...",
//     description: "Máy scan Medit i700 giúp nâng cao dịch vụ chăm sóc răng miệng, khách hàng không còn cảm giác khó chịu như phương pháp lấy dấu thông thường. Quy trình phục hình trở nên đơn giản và nhanh chóng.\n\n" +
//       "Lấy dấu phục hình răng (răng sứ, veneer)\n\n" +
//       "Cung cấp dữ liệu thiết lập kế hoạch implant và chỉnh nha.",
//     type: "video",
//     media_url: "0qglaAr57OA",
//     thumbnailUrl: "https://res.cloudinary.com/dtb2p8vbi/image/upload/v1752938862/M%C3%A1y_Activelink_Plasma_lpjfax.jpg"
//   },
//   {
//     title: "MÁY CTCB",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/v1752938863/M%C3%81Y_CTCB_dwkev8.png",
//     brief: 
//       "Máy scan Medit i700 giúp nâng cao dịch vụ chăm sóc răng miệng, khách hàng không còn cảm giác khó chịu như phương pháp lấy dấu thông thường. Quy trình phục hình trở nên đơn giản và nhanh chóng.\n" +
//       "...",
//     description: "Máy scan Medit i700 giúp nâng cao dịch vụ chăm sóc răng miệng, khách hàng không còn cảm giác khó chịu như phương pháp lấy dấu thông thường. Quy trình phục hình trở nên đơn giản và nhanh chóng.\n\n" +
//       "Lấy dấu phục hình răng (răng sứ, veneer)\n\n" +
//       "Cung cấp dữ liệu thiết lập kế hoạch implant và chỉnh nha.",
//     type: "image",
//   },
//   {
//     title: "Máy Rayface (Face scan)",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/v1752938866/M%C3%A1y_Rayface_Face_scan_mbctev.png",
//     brief: 
//       "Máy scan Medit i700 giúp nâng cao dịch vụ chăm sóc răng miệng, khách hàng không còn cảm giác khó chịu như phương pháp lấy dấu thông thường. Quy trình phục hình trở nên đơn giản và nhanh chóng.\n" +
//       "...",
//     description: "Máy scan Medit i700 giúp nâng cao dịch vụ chăm sóc răng miệng, khách hàng không còn cảm giác khó chịu như phương pháp lấy dấu thông thường. Quy trình phục hình trở nên đơn giản và nhanh chóng.\n\n" +
//       "Lấy dấu phục hình răng (răng sứ, veneer)\n\n" +
//       "Cung cấp dữ liệu thiết lập kế hoạch implant và chỉnh nha.",
//     type: "image",
//   },
//   {
//     title: "MÁY SCAN LẤY DẤU MEDIT i700",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/v1752938861/MEDIT_LINK_j4qv22.webp",
//     brief: 
//       "Máy scan Medit i700 giúp nâng cao dịch vụ chăm sóc răng miệng, khách hàng không còn cảm giác khó chịu như phương pháp lấy dấu thông thường. Quy trình phục hình trở nên đơn giản và nhanh chóng.\n" +
//       "...",
//     description: "Máy scan Medit i700 giúp nâng cao dịch vụ chăm sóc răng miệng, khách hàng không còn cảm giác khó chịu như phương pháp lấy dấu thông thường. Quy trình phục hình trở nên đơn giản và nhanh chóng.\n\n" +
//       "Lấy dấu phục hình răng (răng sứ, veneer)\n\n" +
//       "Cung cấp dữ liệu thiết lập kế hoạch implant và chỉnh nha.",
//     type: "image",
//   },
//   {
//     title: "MÁY LASER LX 16 PLUS",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/v1752938862/Laser_LX16_ul8jqs.webp",
//     brief:
//       'Laser nha khoa là phương pháp thực hiện các thủ thuật và phẫu thuật các mô mềm nhỏ trong miệng với mức xâm lấn tối thiểu và khả năng chữa lành nhanh chóng.\n\n' +
//       "Ứng dụng laser trong nha khoa mang lại nhiều lợi ích:\n ...",
//     description: "Laser nha khoa là phương pháp thực hiện các thủ thuật và phẫu thuật các mô mềm nhỏ trong miệng với mức xâm lấn tối thiểu và khả năng chữa lành nhanh chóng\n\n" +
//       "Ứng dụng laser trong nha khoa mang lại nhiều lợi ích:\n" +
//       "- Giảm đau, cầm máu, kháng viêm\n" +
//       "- Hỗ trợ tăng tốc độ di chuyển răng trong chỉnh nha\n" +
//       "- Lành thương nhanh sau nhổ răng khôn hay các vết thương tiểu phẫu\n" +
//       "- Tẩy trắng răng bằng laser\n",
//     type: "image",
    
//   },
//   {
//     title: "MÁY SIÊU ÂM PEIZO",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/v1752938864/M%C3%A1y_si%C3%AAu_%C3%A2m_peizo_h7kejw.jpg",
//     brief: 
//       "Hỗ trợ cắt xương với độ chính xác cao không tổn hại đến mô mềm hay mạch máu xung quanh.\n\n" +
//       "Giảm chấn thương mô mềm và các dây thần kinh khi nhổ răng khôn.\n...",
//     description: "Hỗ trợ cắt xương với độ chính xác cao không tổn hại đến mô mềm hay mạch máu xung quanh\n\n" +
//       "Giảm chấn thương mô mềm và các dây thần kinh khi nhổ răng khôn.\n\n" +
//       "Vết thương mau lành và giảm nguy cơ nhiễm trùng.",
//     type: "image",
//   }
// ];

const Equipment = () => {
  const equipmentTitle = usePage().props.equipmentTitle as Title;
  const equipment = usePage().props.equipments as EquipmentType[];

  const [hoveredItem, setHoveredItem] = useState<number | null>(null);
  const [selectedMedia, setSelectedMedia] = useState<any>(null);
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  const goToSlide = (index: number) => {
    if (api) {
      api.scrollTo(index);
    }
  };

  const handleMediaClick = (activity: typeof equipment[0]) => {
    setSelectedMedia({
      type: activity.type,
      title: activity.title,
      description: activity.description,
      media_url: activity.media_url,
      image: activity.image,
    });
  };

  return (
    <>
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{equipmentTitle?.main_title ?? "Hệ Thống Trang Thiết Bị Hiện Đại"}</h2>
            {/* <div className="w-24 h-1 bg-emerald-600 mx-auto mb-8"></div> */}
            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {equipmentTitle?.subtitle ?? "Thiết bị hiện đại của chúng tôi đảm bảo chăm sóc răng miệng chính xác, thoải mái và hiệu quả với những cải tiến công nghệ mới nhất."}
            </p>
          </div>

          <div className="relative max-w-6xl mx-auto">
          <Carousel
            setApi={setApi}
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent className="-ml-2 md:-ml-4">
              {equipment.map((item, index) => (
                <CarouselItem key={index} className="pl-2 md:pl-4 md:basis-1/4">
                  <div
                    className="relative group cursor-pointer overflow-hidden rounded-2xl shadow-lg bg-white"
                    onMouseEnter={() => setHoveredItem(index)}
                    onMouseLeave={() => setHoveredItem(null)}
                  >
                    <div className="relative h-80 overflow-hidden">
                      <img
                        src={item.image}
                        alt={item.title}
                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                      />
                      
                      {/* Overlay */}
                      <div className={`absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent transition-opacity duration-300 ${
                        hoveredItem === index ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'
                      }`}>
                        <div className="absolute bottom-0 left-0 right-0 p-6 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-500 z-10">
                          <h3 className="text-xl font-bold mb-3">{item.title}</h3>
                          <p className="text-sm mb-4 opacity-90 whitespace-pre-wrap">{item.brief}</p>
                          
                          <Button
                            size="sm"
                            variant="landing_outline"
                            className="text-white relative z-20 pointer-events-auto"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleMediaClick(item);
                            }}
                          >
                            Xem Thêm
                          </Button>
                        </div>
                      </div>
                    </div>
                    
                    {/* Default visible content */}
                    <div className={`absolute bottom-0 left-0 right-0 p-6 text-white bg-gradient-to-t from-black/60 to-transparent transition-opacity duration-300 ${
                      hoveredItem === index ? 'opacity-0' : 'opacity-100'
                    }`}>
                      <h3 className="text-xl font-bold">{item.title}</h3>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            {/* <CarouselPrevious className="left-4" />
            <CarouselNext className="right-4" /> */}
          </Carousel>

          {/* Slide Indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {Array.from({ length: count }).map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === current - 1
                    ? 'bg-emerald-600' 
                    : 'bg-gray-400 hover:bg-emerald-400'
                }`}
              />
            ))}
          </div>
          </div>
          
          <div className="text-center mt-12">
            <Link href={"/contact"}>
              <Button className="rounded-full hover:shadow-lg hover:-translate-y-1">
                Đặt lịch tư vấn ngay bây giờ!
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <MediaModal
        isOpen={!!selectedMedia}
        onClose={() => setSelectedMedia(null)}
        type={selectedMedia?.type || "image"}
        title={selectedMedia?.title || ""}
        description={selectedMedia?.description}
        image={selectedMedia?.image}
        media_url={selectedMedia?.media_url || ""}
      />
    </>
  );
};

export default Equipment;