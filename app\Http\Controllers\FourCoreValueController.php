<?php

namespace App\Http\Controllers;

use App\Models\FourCoreValue;
use App\Models\SectionTitle;
use Illuminate\Http\Request;

class FourCoreValueController extends Controller
{
    public function store(Request $request)
    {
        SectionTitle::create($request->all());
        $this->handleFourCoreValues($request);
        return back();
    }
    public function update(Request $request, SectionTitle $sectionTitle)
    {
        $sectionTitle->update($request->all());
        $this->handleFourCoreValues($request);
        return back();
    }

    public function handleFourCoreValues(Request $request)
    {
        $fourCoreValues = $request['core_values'];

        FourCoreValue::first()
            ? FourCoreValue::first()->update(['core_values' => $fourCoreValues])
            : FourCoreValue::create(['core_values' => $fourCoreValues]);
    }
}
