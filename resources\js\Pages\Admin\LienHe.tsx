import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/Components/ui/card";
import { <PERSON><PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/Components/ui/tabs";
import { Button } from "@/Components/ui/button";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import { Textarea } from "@/Components/ui/textarea";
import { Switch } from "@/Components/ui/switch";
import { Badge } from "@/Components/ui/badge";
import { Separator } from "@/Components/ui/separator";
import { 
  Settings, 
  Eye, 
  Save, 
  Plus, 
  Trash2, 
  Image,
  FileText,
  Users,
  Calendar,
  Phone,
  MapPin,
  Clock,
  Mail,
  Link
} from "lucide-react";
import { toast } from "sonner";
import AppLayout from "@/Layouts/AppLayout";
import { Head } from "@inertiajs/react";

const Admin = () => {
  // Hero Section State
  const [contactData, setContactData] = useState({
    main_title: "<PERSON>ên <PERSON>ệ Với Chúng Tôi",
    // title_highlight: "Our Priority", 
    subtitle: "Bạn đã sẵn sàng đặt lịch hẹn? Hãy liên hệ với chúng tôi ngay hôm nay để bắt đầu hành trình hướng tới một nụ cười khỏe đẹp hơn.",
    background_images: [
      "https://images.unsplash.com/photo-1629909613654-28e377c37b09?auto=format&fit=crop&q=80&w=1920",
      "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?auto=format&fit=crop&q=80&w=1920",
      "https://images.unsplash.com/photo-1606811841689-23dfddce3e95?auto=format&fit=crop&q=80&w=1920",
      "https://images.unsplash.com/photo-1588776814546-1ffcf47267a5?auto=format&fit=crop&q=80&w=1920"
    ],
    contact_info: {
      phone: "0273.6588.988",
      phone2: "0939.809.246",
      location: "123 Nguyen Thi Thap, Phu Nhuan, HCM", 
      email: "<EMAIL>",
      googleMapLink: "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3924.8109477379166!2d106.67020109678955!3d10.356996300000015!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31754f002909cebd%3A0x48e8296554c936ed!2sNha%20Khoa%20246%20-USmile%20Dental%20Office!5e0!3m2!1svi!2s!4v1752676492429!5m2!1svi!2s"
    }
  });

  const handleSave = (section: string) => {
    toast.success("Cập nhật thành công!");
  };

  const breadcrumbs = [
    {
      title: "Trang Liên Hệ",
      href: "/admin/lien-he",
    },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Trang Liên Hệ" />
      <div className="flex h-full flex-1 flex-col space-y-8 p-4">

        <div className="min-h-screen bg-background">
          {/* Admin Header */}
          <div className="bg-card">
            <div className="container mx-auto">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                    <Settings className="w-5 h-5 text-primary-foreground" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold">Nha Khoa 246 Admin</h1>
                    <p className="text-muted-foreground">Quản lý nội dung trang liên hệ</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" onClick={() => window.open('/contact', '_blank')}>
                    <Eye className="w-4 h-4" />
                    Xem Trang Liên Hệ
                  </Button>
                  {/* <Badge variant="secondary">Live</Badge> */}
                </div>
              </div>
            </div>
          </div>

          <div className="container mx-auto px-4 py-8">
            <Tabs defaultValue="contact" className="space-y-6">
              <TabsList className="grid w-full grid-cols-6">
                <TabsTrigger value="contact">Liên Hệ</TabsTrigger>
              </TabsList>

              {/* contact Section */}
              <TabsContent value="contact" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      Liên Hệ
                    </CardTitle>
                    <CardDescription>Quản lý nội dung liên hệ</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid md:grid-cols-1 gap-6">
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="main_title">Tiêu đề</Label>
                          <Input
                            id="main_title"
                            value={contactData.main_title}
                            onChange={(e) => setContactData({...contactData, main_title: e.target.value})}
                          />
                        </div>
                        <div>
                          <Label htmlFor="subtitle">Phụ đề</Label>
                          <Textarea
                            id="subtitle"
                            value={contactData.subtitle}
                            onChange={(e) => setContactData({...contactData, subtitle: e.target.value})}
                            rows={3}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        {/* <h4 className="font-medium">Số Điện Thoại, Email, Địa Chỉ</h4> */}
                        <Label htmlFor="contact_info">Thông tin liên hệ <span className="text-destructive">*(Lấy từ Thông Tin Chung)</span></Label>
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <Phone className="w-4 h-4" />
                            <div className="flex flex-col md:flex-row w-full gap-2">
                                <Input
                                    value={contactData.contact_info.phone}
                                    onChange={(e) => setContactData({
                                        ...contactData, 
                                        contact_info: {...contactData.contact_info, phone: e.target.value}
                                    })}
                                    placeholder="Số điện thoại 1"
                                    disabled
                                />
                                <Input
                                    value={contactData.contact_info.phone2}
                                    onChange={(e) => setContactData({
                                        ...contactData, 
                                        contact_info: {...contactData.contact_info, phone2: e.target.value}
                                    })}
                                    placeholder="Số điện thoại 2"
                                    disabled
                                />
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Mail className="w-4 h-4" />
                            <Input
                              value={contactData.contact_info.email}
                              onChange={(e) => setContactData({
                                ...contactData, 
                                contact_info: {...contactData.contact_info, email: e.target.value}
                              })}
                              placeholder="Email"
                              disabled
                            />
                          </div>
                          <div className="flex items-center space-x-2">
                            <MapPin className="w-4 h-4" />
                            <Input
                              value={contactData.contact_info.location}
                              onChange={(e) => setContactData({
                                ...contactData, 
                                contact_info: {...contactData.contact_info, location: e.target.value}
                              })}
                              placeholder="Địa chỉ"
                              disabled
                            />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="contact_info">Google Map <span className="text-destructive">*(Lấy từ Thông Tin Chung)</span></Label>
                        {/* <h4 className="font-medium">Link Địa chỉ GG Map</h4> */}
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <Link className="w-4 h-4" />
                            <Input
                              value={contactData.contact_info.googleMapLink}
                              onChange={(e) => setContactData({
                                ...contactData, 
                                contact_info: {...contactData.contact_info, googleMapLink: e.target.value}
                              })}
                              disabled
                            />
                            </div>
                            <div className="w-full h-60 rounded-lg overflow-hidden">
                                <iframe
                                    src={contactData.contact_info.googleMapLink}
                                    width="100%"
                                    height="100%"
                                    style={{ border: 0 }}
                                    allowFullScreen
                                    loading="lazy"
                                    referrerPolicy="no-referrer-when-downgrade"
                                    title="Nha khoa 246"
                                    className="pointer-events-none"
                                />
                            </div>
                          </div>
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Button onClick={() => handleSave("Hero")}>
                        <Save className="w-4 h-4 mr-2" />
                        Save Changes
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default Admin;