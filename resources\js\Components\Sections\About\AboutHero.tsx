import { Button } from "@/Components/ui/button";
import { Link } from "@inertiajs/react";

const AboutHero = () => {
  return (
    <div className="relative min-h-screen bg-gradient-to-br from-emerald-50 to-white">
      {/* Hero Content */}
      <div className="container mx-auto px-4 pt-32 pb-20">
        <div className="text-center max-w-4xl mx-auto">
          <div className="space-y-8 animate-fade-in">
            <h1 className="text-6xl lg:text-7xl font-bold text-gray-900 leading-tight mb-8">
              Về Chúng Tôi
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto mb-12">
              Khám phá câu chuyện phía sau Nha Khoa 246 – nơi công nghệ tiên tiến kết hợp cùng sự tận tâm trong chăm sóc, mang đến trải nghiệm nha khoa vượt trội cho từng khách hàng.
            </p>
            
            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              {/* <Button>
                Dịch Vụ Của Chúng Tôi
              </Button> */}
              <Link href="/contact">
                <Button variant="landing_outline">
                  Liên Hệ Với Chúng Tôi
                </Button>
              </Link>
            </div>
            
            {/* Stats or Highlights */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
              <div className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                <div className="text-3xl font-bold text-emerald-600 mb-2">5+</div>
                <div className="text-gray-700 font-medium">Năm Kinh Nghiệm</div>
              </div>
              <div className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                <div className="text-3xl font-bold text-emerald-600 mb-2">500+</div>
                <div className="text-gray-700 font-medium">Khách Hàng Hài Lòng</div>
              </div>
              <div className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                <div className="text-3xl font-bold text-emerald-600 mb-2">4.9★</div>
                <div className="text-gray-700 font-medium">Đánh Giá Từ Khách Hàng</div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Background decorative elements */}
        <div className="absolute top-20 right-10 w-32 h-32 bg-emerald-100 rounded-full opacity-50 -z-10"></div>
        <div className="absolute bottom-20 left-10 w-24 h-24 bg-green-100 rounded-full opacity-50 -z-10"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-purple-100 rounded-full opacity-30 -z-10"></div>
      </div>
    </div>
  );
};

export default AboutHero;