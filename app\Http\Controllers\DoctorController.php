<?php

namespace App\Http\Controllers;

use App\Models\Doctor;
use App\Models\SectionTitle;
use Illuminate\Http\Request;

class DoctorController extends Controller
{
    public function store(Request $request)
    {
        SectionTitle::create($request->all());
        $this->handleDoctors($request);
        return back();
    }
    public function update(Request $request, SectionTitle $sectionTitle)
    {
        $sectionTitle->update($request->all());
        $this->handleDoctors($request);
        return back();
    }
    public function handleDoctors(Request $request)
    {
        $doctors = $request['doctors'];

        // Filter out only valid integer IDs (existing records)
        $existingIds = array_filter(array_map(fn($doctor) =>
            is_int($doctor['id']) ? $doctor['id'] : null
        , $doctors));

        // Delete records that are not in the current list of existing IDs
        if (!empty($existingIds)) {
            Doctor::whereNotIn('id', $existingIds)->delete();
        } else {
            // If no existing IDs, delete all records
            Doctor::truncate();
        }

        foreach ($doctors as $doctor) {
            if (is_int($doctor['id'])) {
                // Update existing record
                $existingRecord = Doctor::find($doctor['id']);
                if ($existingRecord) {
                    $existingRecord->update($doctor);
                }
            } else {
                // Create new record (remove the temporary ID)
                unset($doctor['id']);
                Doctor::create($doctor);
            }
        }
    }
}
