<?php

namespace App\Http\Controllers;

use App\Models\Equipment;
use App\Models\SectionTitle;
use Illuminate\Http\Request;

class EquipmentController extends Controller
{
    public function store(Request $request)
    {
        SectionTitle::create($request->all());
        $this->handleEquipments($request);
        return back();
    }
    public function update(Request $request, SectionTitle $sectionTitle)
    {
        $sectionTitle->update($request->all());
        $this->handleEquipments($request);
        return back();
    }

    public function handleEquipments(Request $request)
    {
        $equipments = $request['equipments'];

        // Filter out only valid integer IDs (existing records)
        $existingIds = array_filter(array_map(fn($equipment) =>
            is_int($equipment['id']) ? $equipment['id'] : null
        , $equipments));

        // Delete records that are not in the current list of existing IDs
        if (!empty($existingIds)) {
            Equipment::whereNotIn('id', $existingIds)->delete();
        } else {
            // If no existing IDs, delete all records
            Equipment::truncate();
        }

        foreach ($equipments as $equipment) {
            if (is_int($equipment['id'])) {
                // Update existing record
                $existingRecord = Equipment::find($equipment['id']);
                if ($existingRecord) {
                    $existingRecord->update($equipment);
                }
            } else {
                // Create new record (remove the temporary ID)
                unset($equipment['id']);
                Equipment::create($equipment);
            }
        }
    }
}
