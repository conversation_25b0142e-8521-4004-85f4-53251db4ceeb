import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle, CardDescription } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Image, Plus, Save, Trash2, Users } from "lucide-react";
import { useEffect, useState } from "react";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import { Textarea } from "@/Components/ui/textarea";
import { Separator } from "@/Components/ui/separator";
import { toast } from "sonner";
import { router, usePage } from "@inertiajs/react";
import { Title, Team as TeamType } from "@/types";

const Team = () => {
    const teamTitle = usePage().props.teamTitle as Title;
    const teams = usePage().props.teams as TeamType[];

    const [teamData, setTeamData] = useState({
        id: teamTitle?.id ?? 0,
        main_title: teamTitle?.main_title ?? "Your Smile,",
        subtitle: teamTitle?.subtitle ?? "Experience exceptional dental care with compassionate professionals dedicated to your comfort and oral health.",
        section_name: "teams",
        people_list: teams ?? []
    });

    useEffect(() => {
        if (!teamTitle) return;

        setTeamData({...teamTitle, people_list: teams.length > 0 ? teams : []});
    }, [teamTitle, teams]);

    const handleSave = () => {
        if (teamData.id !== 0) {
            router.put(
                route('teams.update', teamData.id), 
                teamData as any, 
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
            
        } else {
            router.post(route('teams.store'), 
                teamData as any,
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
        }
    };
    
    return (
        <Card>
            <CardHeader>
            <CardTitle className="flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Danh sách nhân viên
            </CardTitle>
            <CardDescription>Quản lý nội dung và hình ảnh của nhân viên</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
            <div className="grid md:grid-cols-1 gap-6">
                <div className="space-y-4">
                    <div>
                        <Label htmlFor="main_title">Tiêu đề</Label>
                        <Input
                        id="main_title"
                        value={teamData.main_title}
                        onChange={(e) => setTeamData({...teamData, main_title: e.target.value})}
                        />
                    </div>
                    <div>
                        <Label htmlFor="subtitle">Phụ đề</Label>
                        <Textarea
                        id="subtitle"
                        value={teamData.subtitle}
                        onChange={(e) => setTeamData({...teamData, subtitle: e.target.value})}
                        rows={3}
                        />
                    </div>
                </div>
            </div>

            <Separator />
            
            <div>
                <h4 className="font-medium mb-4">Danh sách nhân viên</h4>
                <div className="space-y-3">
                    {teamData.people_list.map((person, index) => (
                        <div key={index} className="flex items-center space-x-2">
                            <span className="text-sm text-muted-foreground w-8">#{index + 1}</span>
                            <Input
                                value={person.position}
                                onChange={(e) => {
                                    const updated = [...teamData.people_list];
                                    updated[index].position = e.target.value;
                                    setTeamData({...teamData, people_list: updated});
                                }}
                                placeholder="Chức vụ"
                                className="w-1/4"
                            />
                            <Input
                                value={person.image}
                                onChange={(e) => {
                                    const updated = [...teamData.people_list];
                                    updated[index].image = e.target.value;
                                    setTeamData({...teamData, people_list: updated});
                                }}
                                placeholder="Hình ảnh"
                            />
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                const updated = teamData.people_list.filter((_, i) => i !== index);
                                setTeamData({...teamData, people_list: updated});
                                }}
                            >
                                <Trash2 className="w-4 h-4" />
                            </Button>
                        </div>
                    ))}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setTeamData({
                        ...teamData, 
                        people_list: [...teamData.people_list, {image: "", position: ""}]
                        })}
                    >
                        <Plus className="w-4 h-4" />
                        Thêm hình ảnh
                    </Button>
                </div>
            </div>

            <div className="flex justify-end">
                <Button onClick={() => handleSave()}>
                    <Save className="w-4 h-4" />
                    Lưu thay đổi
                </Button>
            </div>
            </CardContent>
        </Card>
    )
}

export default Team;