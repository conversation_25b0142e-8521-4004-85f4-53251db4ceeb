<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use App\Models\SectionTitle;
use Illuminate\Http\Request;

class ActivityController extends Controller
{
    public function store(Request $request)
    {
        SectionTitle::create($request->all());
        $this->handleActivities($request);
        return back();
    }
    public function update(Request $request, SectionTitle $sectionTitle)
    {
        $sectionTitle->update($request->all());
        $this->handleActivities($request);
        return back();
    }

    public function handleActivities(Request $request)
    {
        $activities = $request['activities'];

        // Filter out only valid integer IDs (existing records)
        $existingIds = array_filter(array_map(fn($activity) =>
            is_int($activity['id']) ? $activity['id'] : null
        , $activities));

        // Delete records that are not in the current list of existing IDs
        if (!empty($existingIds)) {
            Activity::whereNotIn('id', $existingIds)->delete();
        } else {
            // If no existing IDs, delete all records
            Activity::truncate();
        }

        foreach ($activities as $activity) {
            if (is_int($activity['id'])) {
                // Update existing record
                $existingRecord = Activity::find($activity['id']);
                if ($existingRecord) {
                    $existingRecord->update($activity);
                }
            } else {
                // Create new record (remove the temporary ID)
                unset($activity['id']);
                Activity::create($activity);
            }
        }
    }
}
