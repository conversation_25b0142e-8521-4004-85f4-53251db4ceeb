import { Card, CardContent } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Play, Calendar, Users } from "lucide-react";
import { useState } from "react";
import MediaModal from "@/Components/MediaModal";

const Activities = () => {
    const [selectedMedia, setSelectedMedia] = useState<{
        type: "image" | "video";
        title: string;
        description: string;
        media_url: string;
        thumbnailUrl?: string;
    } | null>(null);

    const cloudinaryImageUrl = "https://res.cloudinary.com/dooloewyi/image/upload/v1751274730/hoc-vien-nha-khoa_o4qn1o.jpg";

  const activities = [
    {
      id: 1,
      title: "Kiểm Tra Nha Khoa Miễn Ph<PERSON>",
      description: "Kiểm tra nha khoa miễn phí cho các gia đình nghèo ở <PERSON>, <PERSON><PERSON><PERSON><PERSON>.",
      image: "https://images.unsplash.com/photo-1551601651-2a8555f1a136?auto=format&fit=crop&q=80&w=1200",
      type: "image" as const,
      date: "Tháng Ba 2024",
      participants: "200+ gia đình",
      media_url: "https://images.unsplash.com/photo-1551601651-2a8555f1a136?auto=format&fit=crop&q=80&w=1200"
    },
    {
      id: 2,
      title: "Học Viện Nha Khoa",
      description: "Học viện nha khoa dành cho các bác sĩ nha khoa mới",
      image: cloudinaryImageUrl,
      type: "video" as const,
      date: "Tháng Hai 2024",
      participants: "50+ bác sĩ",
      media_url: "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
      thumbnailUrl: cloudinaryImageUrl
    },
    {
      id: 3,
      title: "Chương Trình Giáo Dục Nha Khoa Cho Trẻ Em",
      description: "Giáo dục nha khoa cho trẻ em bao gồm các buổi tư vấn và trải nghiệm nha khoa",
      image: "images/nha-khoa-tre-em-2.jpg",
      type: "image" as const,
      date: "Tháng Một 2024",
      participants: "300+ trẻ em",
      media_url: "images/nha-khoa-tre-em-2.jpg"
    },
    {
      id: 4,
      title: "Hiện Thực Hóa Ước Mơ Nha Khoa",
      description: "Hiện thực hóa ước mơ nha khoa của bạn bằng các thiết bị nha khoa mới nhất",
      image: "images/healthy-smile.jpg",
      type: "video" as const,
      date: "Tháng Mười Hai 2023",
      participants: "100+ khách hàng",
      media_url: "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4",
      thumbnailUrl: "images/healthy-smile.jpg"
    }
  ];

  const handleMediaClick = (activity: typeof activities[0]) => {
    setSelectedMedia({
      type: activity.type,
      title: activity.title,
      description: activity.description,
      media_url: activity.media_url,
      thumbnailUrl: activity.thumbnailUrl
    });
  };

  return (
    <>
      <section className="py-20 bg-white">
          <div className="container mx-auto px-4">
              <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Những Hoạt Động Nổi Bật Tại Nha Khoa 246</h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                  Khám phá các hoạt động và dịch vụ nổi bật tại nha khoa của chúng tôi – từ chăm sóc răng miệng tổng quát đến các giải pháp thẩm mỹ hiện đại. Nha Khoa 246 luôn đặt chất lượng điều trị và sự hài lòng của bạn lên hàng đầu.
              </p>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
              {activities.map((activity) => (
                  <Card key={activity.id} className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
                  <div className="relative cursor-pointer" onClick={() => handleMediaClick(activity)}>
                      <img 
                        src={activity.image} 
                        alt={activity.title}
                        className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      {activity.type === "video" && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 group-hover:bg-opacity-40 transition-all duration-300">
                            <div className="bg-white bg-opacity-90 rounded-full p-4 group-hover:scale-110 transition-transform duration-300">
                            <Play className="w-8 h-8 text-emerald-600" />
                            </div>
                        </div>
                      )}
                      <div className="absolute top-4 right-4">
                      <span className="bg-emerald-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                          {activity.type === "video" ? "Video" : "Hình Ảnh"}
                      </span>
                      </div>
                  </div>
                  
                  <CardContent className="p-6">
                      <h3 className="text-xl font-semibold text-gray-900 mb-3">{activity.title}</h3>
                      <p className="text-gray-600 mb-4 leading-relaxed">{activity.description}</p>
                      
                      <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          {activity.date}
                      </div>
                      <div className="flex items-center">
                          <Users className="w-4 h-4 mr-1" />
                          {activity.participants}
                      </div>
                      </div>
                      
                      <Button 
                        variant="landing_outline" 
                        className="w-full"
                        onClick={() => handleMediaClick(activity)}
                      >
                        {activity.type === "video" ? "Xem Video" : "Xem Hình Ảnh"}
                      </Button>
                  </CardContent>
                  </Card>
              ))}
              </div>
          </div>
      </section>
      
      <MediaModal
          isOpen={!!selectedMedia}
          onClose={() => setSelectedMedia(null)}
          type={selectedMedia?.type || "image"}
          title={selectedMedia?.title || ""}
          description={selectedMedia?.description}
          media_url={selectedMedia?.media_url || ""}
      />
    </>
  );
};

export default Activities;