<?php

namespace App\Http\Controllers;

use App\Models\CommonInfo;
use App\Models\Doctor;
use App\Models\ResourceHero;
use App\Models\SectionTitle;
use App\Models\Team;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ResourceController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Admin/DoiNguNhaKhoa', [
            'resourceHero' => ResourceHero::first(),
            'doctorTitle' => SectionTitle::where('section_name', 'doctors')->first(),
            'doctors' => Doctor::all(),
            'teamTitle' => SectionTitle::where('section_name', 'teams')->first(),
            'teams' => Team::first() ? Team::first()->people_list : [],
            'contactTitle' => SectionTitle::where('section_name', 'contacts')->first(),
            'commonInfo' => CommonInfo::first(),
        ]);
    }
}
