import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle, CardDescription } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Image, Save } from "lucide-react";
import { useEffect, useState } from "react";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import { Textarea } from "@/Components/ui/textarea";
import { toast } from "sonner";
import { router, usePage } from "@inertiajs/react";
import { ResourceHero } from "@/types";

const Hero = () => {
    const resourceHero = usePage().props.resourceHero as ResourceHero;

    // Hero Section State
    const [heroData, setHeroData] = useState({
        id: resourceHero?.id ?? 0,
        main_title: resourceHero?.main_title ?? "Nha Khoa 246",
        title_highlight: resourceHero?.title_highlight ?? "Your Smile, Our Priority",
        subtitle: resourceHero?.subtitle ?? "Trung tâm nha khoa hiện đại, độ<PERSON> ngũ bác sĩ tận tâm – nơi bạn luôn cảm thấy an toàn, thõa mái và được chăm sóc tận tình.",
        background_video: resourceHero?.background_video ?? ""
    });
    
    useEffect(() => {
        if (!resourceHero) return;

        setHeroData(resourceHero);
    }, [resourceHero]);

    const handleSave = () => {
        if (heroData.id !== 0) {
            router.put(
                route('resource-hero.update', heroData.id), 
                heroData, 
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
            
        } else {
            router.post(route('resource-hero.store'), 
                heroData,
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
        }
    };
    
    return (
        <Card>
            <CardHeader>
            <CardTitle className="flex items-center">
                <Image className="w-5 h-5 mr-2" />
                Hero trang Đội Ngũ Nha Khoa
            </CardTitle>
            <CardDescription>Quản lý nội dung và video của hero</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
            <div className="grid md:grid-cols-1 gap-6">
                <div className="space-y-4">
                    <div>
                        <Label htmlFor="main_title">Tiêu đề</Label>
                        <Input
                        id="main_title"
                        value={heroData.main_title}
                        onChange={(e) => setHeroData({...heroData, main_title: e.target.value})}
                        />
                    </div>
                    <div>
                        <Label htmlFor="title_highlight">Tiêu đề phụ</Label>
                        <Input
                        id="title_highlight"
                        value={heroData.title_highlight}
                        onChange={(e) => setHeroData({...heroData, title_highlight: e.target.value})}
                        />
                    </div>
                    <div>
                        <Label htmlFor="subtitle">Phụ đề</Label>
                        <Textarea
                        id="subtitle"
                        value={heroData.subtitle}
                        onChange={(e) => setHeroData({...heroData, subtitle: e.target.value})}
                        rows={3}
                        />
                    </div>
                    <div>
                        <Label htmlFor="primary-btn">Video nền</Label>
                        <Input
                        value={heroData.background_video}
                        onChange={(e) => setHeroData({...heroData, background_video: e.target.value})}
                        placeholder="Video URL"
                        />
                    </div>
                </div>
            </div>

            <div className="flex justify-end">
                <Button onClick={() => handleSave()}>
                    <Save className="w-4 h-4" />
                    Lưu thay đổi
                </Button>
            </div>
            </CardContent>
        </Card>
    )
}

export default Hero;