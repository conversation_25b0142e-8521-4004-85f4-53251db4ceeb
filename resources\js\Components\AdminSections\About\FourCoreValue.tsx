import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle, CardDescription } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Image, Save } from "lucide-react";
import { useEffect, useState } from "react";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import { Textarea } from "@/Components/ui/textarea";
import { Separator } from "@/Components/ui/separator";
import { toast } from "sonner";
import { router, usePage } from "@inertiajs/react";
import { Title } from "@/types";

const FourCoreValue = () => {
    const fourCoreValueTitle = usePage().props.fourCoreValueTitle as Title;
    const fourCoreValues = usePage().props.fourCoreValues as string[];

    const [fourCoreValueData, setFourCoreValueData] = useState(
        {
            id: fourCoreValueTitle?.id ?? 0,
            main_title: fourCoreValueTitle?.main_title ?? "Your Smile,",
            subtitle: fourCoreValueTitle?.subtitle ?? "Experience exceptional dental care with compassionate professionals dedicated to your comfort and oral health.",
            section_name: "four_core_values",
            core_values: fourCoreValues.length > 0 ? fourCoreValues : [
                "", "", "", ""
            ]
        }
    );

    useEffect(() => {
        if (!fourCoreValueTitle) return;

        setFourCoreValueData({...fourCoreValueTitle, core_values: fourCoreValues.length > 0 ? fourCoreValues : [
            "", "", "", ""
        ]});
    }, [fourCoreValueTitle, fourCoreValues]);

    const handleSave = () => {
        if (fourCoreValueData.id !== 0) {
            router.put(
                route('four-core-values.update', fourCoreValueData.id), 
                fourCoreValueData, 
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
            
        } else {
            router.post(route('four-core-values.store'), 
                fourCoreValueData,
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
        }
    };
    
    return (
        <Card>
            <CardHeader>
            <CardTitle className="flex items-center">
                <Image className="w-5 h-5 mr-2" />
                4 giá trị cốt lõi
            </CardTitle>
            <CardDescription>Quản lý nội dung và hình ảnh của bốn giá trị cốt lõi</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
            <div className="grid md:grid-cols-1 gap-6">
                <div className="space-y-4">
                    <div>
                        <Label htmlFor="main_title">Tiêu đề</Label>
                        <Input
                        id="main_title"
                        value={fourCoreValueData.main_title}
                        onChange={(e) => setFourCoreValueData({...fourCoreValueData, main_title: e.target.value})}
                        />
                    </div>
                    <div>
                        <Label htmlFor="subtitle">Phụ đề</Label>
                        <Textarea
                        id="subtitle"
                        value={fourCoreValueData.subtitle}
                        onChange={(e) => setFourCoreValueData({...fourCoreValueData, subtitle: e.target.value})}
                        rows={3}
                        />
                    </div>
                </div>
            </div>

            <Separator />
            
            <div>
                <h4 className="font-medium mb-4">Danh sách hình ảnh giá trị cốt lõi</h4>
                <div className="space-y-3">
                    {fourCoreValueData.core_values.map((image, index) => (
                        <div key={index} className="flex items-center space-x-2">
                            <span className="text-sm text-muted-foreground w-8">#{index + 1}</span>
                            <Input
                                value={image}
                                onChange={(e) => {
                                const updated = [...fourCoreValueData.core_values];
                                updated[index] = e.target.value;
                                setFourCoreValueData({...fourCoreValueData, core_values: updated});
                                }}
                                placeholder="Link ảnh"
                            />
                        </div>
                    ))}
                    {/* <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground w-8">#1</span>
                        <Input
                            value={fourCoreValueData.core_values[0]}
                            onChange={(e) => {
                                setFourCoreValueData({...fourCoreValueData, core_values: [e.target.value, ...fourCoreValueData.core_values.slice(1)]});
                            }}
                            placeholder="Link ảnh"
                        />
                    </div>
                    <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground w-8">#2</span>
                        <Input
                            value={fourCoreValueData.core_values[1]}
                            onChange={(e) => {
                                setFourCoreValueData({...fourCoreValueData, core_values: [fourCoreValueData.core_values[0], e.target.value, ...fourCoreValueData.core_values.slice(2)]});
                            }}
                            placeholder="Link ảnh"
                        />
                    </div>
                    <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground w-8">#3</span>
                        <Input
                            value={fourCoreValueData.coreValue3}
                            onChange={(e) => {
                                setFourCoreValueData({...fourCoreValueData, coreValue3: e.target.value});
                            }}
                            placeholder="Link ảnh"
                        />
                    </div>
                    <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground w-8">#4</span>
                        <Input
                            value={fourCoreValueData.coreValue4}
                            onChange={(e) => {
                                setFourCoreValueData({...fourCoreValueData, coreValue4: e.target.value});
                            }}
                            placeholder="Link ảnh"
                        />
                    </div> */}
                    {/* {fourCoreValueData.background_images.map((image, index) => (
                        <div key={index} className="flex items-center space-x-2">
                            <span className="text-sm text-muted-foreground w-8">#{index + 1}</span>
                            <Input
                                value={image}
                                onChange={(e) => {
                                const updated = [...fourCoreValueData.background_images];
                                updated[index] = e.target.value;
                                setFourCoreValueData({...fourCoreValueData, background_images: updated});
                                }}
                                placeholder="Image URL"
                            />
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                const updated = fourCoreValueData.background_images.filter((_, i) => i !== index);
                                setFourCoreValueData({...fourCoreValueData, background_images: updated});
                                }}
                            >
                                <Trash2 className="w-4 h-4" />
                            </Button>
                        </div>
                    ))}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setFourCoreValueData({
                        ...fourCoreValueData, 
                        background_images: [...fourCoreValueData.background_images, ""]
                        })}
                    >
                        <Plus className="w-4 h-4" />
                        Thêm hình ảnh
                    </Button> */}
                </div>
            </div>

            <div className="flex justify-end">
                <Button onClick={() => handleSave()}>
                    <Save className="w-4 h-4" />
                    Lưu thay đổi
                </Button>
            </div>
            </CardContent>
        </Card>
    )
}

export default FourCoreValue;