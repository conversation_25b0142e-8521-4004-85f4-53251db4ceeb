<?php

namespace App\Http\Controllers;

use App\Models\AboutHero;
use Illuminate\Http\Request;

class AboutHeroController extends Controller
{
    public function store(Request $request)
    {
        AboutHero::create($request->all());
        return back();
    }
    public function update(Request $request, AboutHero $aboutHero)
    {
        $aboutHero->update($request->all());
        return back();
    }
}
