<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use App\Models\CommonInfo;
use App\Models\CustomerReview;
use App\Models\HomeHero;
use App\Models\SectionTitle;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Application;

class HomeClientController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Site/Home', [
            'homeHero' => HomeHero::first(),
            'activitiesTitle' => SectionTitle::where('section_name', 'activities')->first(),
            'activities' => Activity::all(),
            'customerReviewTitle' => SectionTitle::where('section_name', 'customer_reviews')->first(),
            'customerReviews' => CustomerReview::all(),
            'commonInfo' => CommonInfo::first(),

            
            'canLogin' => Route::has('login'),
            'canRegister' => Route::has('register'),
            'laravelVersion' => Application::VERSION,
            'phpVersion' => PHP_VERSION,
        ]);
    }
}
