
import { But<PERSON> } from "@/Components/ui/button";
import { AboutHero as AboutHeroType } from "@/types";
import { Link, usePage } from "@inertiajs/react";

const AboutHero = () => {
  // const cloudName = usePage().props.cloudName;
  // const publicId = "hero_im6uqo";

  const aboutHero = usePage().props.aboutHero as AboutHeroType;

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Background Video */}
      <div className="absolute inset-0">
        <video
          autoPlay
          muted
          loop
          playsInline
          className="w-full h-full object-cover"
        >
          
          <source src={aboutHero?.background_video ?? `https://res.cloudinary.com/dtb2p8vbi/video/upload/v1752813496/hero_im6uqo.mp4`} type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* Video overlay */}
        <div className="absolute inset-0 bg-white/40" />
      </div>

      {/* Hero Content */}
      <div className="relative z-10 container mx-auto px-4 pt-32 pb-20">
        <div className="text-center max-w-4xl mx-auto">
          <div className="space-y-6 sm:space-y-8 animate-fade-in">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight mb-8">
              {aboutHero?.main_title ?? "Về Chúng Tôi"}
            </h1>
            <p className="text-lg text-gray-800 leading-relaxed max-w-3xl mx-auto mb-12">
              {aboutHero?.subtitle ?? "Khám phá câu chuyện phía sau Nha Khoa 246 – nơi công nghệ tiên tiến kết hợp cùng sự tận tâm trong chăm sóc, mang đến trải nghiệm nha khoa vượt trội cho từng khách hàng."}
            </p>
            
            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <Link href="/contact">
                <Button>
                  {aboutHero?.primary_button_text ?? "Liên Hệ Với Chúng Tôi"}
                </Button>
              </Link>
            </div>
            
            {/* Stats or Highlights */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-8 max-w-3xl mx-auto">
              {aboutHero?.stats?.map((stat, index) => (
                <div className="text-center p-4 sm:p-6 bg-black/10 backdrop-blur-sm rounded-2xl shadow-lg border border-black/20" key={index}>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.number}</div>
                  <div className="text-gray-800 font-medium">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Background decorative elements */}
        {/* <div className="absolute top-20 right-10 w-32 h-32 bg-black/10 rounded-full opacity-50 -z-10"></div>
        <div className="absolute bottom-20 left-10 w-24 h-24 bg-black/10 rounded-full opacity-50 -z-10"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-black/10 rounded-full opacity-30 -z-10"></div> */}
      </div>
    </div>
  );
};

export default AboutHero;
