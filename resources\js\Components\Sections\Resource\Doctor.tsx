import { usePage } from "@inertiajs/react";
import { Doctor as DoctorType } from "@/types";

// const doctors = [
//   {
//     id: 1,
//     title: "BSCKII. Nguyễn Nhật Đăng <PERSON>",
//     description: "Giám đốc chuyên môn – Nha khoa 246 – USmile Dental Office\n"
//       + "Chuyên sâu về cấy ghép Implant & Chỉnh hình Răng Hàm Mặt\n\n"
//       + "BSCKII. Nguyễn Nhật Đăng Huân là bác sĩ chuyên khoa Răng Hàm Mặt với hơn 10 năm kinh nghiệm điều trị, giảng dạy và phát triển chuyên môn trong các lĩnh vực cấy ghép Implant, chỉnh nha, phục hình thẩm mỹ và điều trị toàn diện nụ cười. Hiện ông đang giữ vị trí Giám đốc chuyên môn tại Nha khoa 246",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/BS_Hu%C3%A2n_y6dbpi.png",
//     position: "right" as const
//   },
//   {
//     id: 2,
//     title: "BSCKI. Nguyễn Ngọc Đan Thanh",
//     description: "BSCKI. Nguyễn Ngọc Đan Thanh là một bác sĩ chuyên khoa Răng Hàm Mặt đầy tâm huyết. Với nền tảng chuyên môn vững chắc cùng phong cách làm việc tỉ mỉ, BS Đan Thanh đặc biệt được biết đến trong các lĩnh vực điều trị nha khoa và phục hình thẩm mỹ Veneer.",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/BS_Thanh_czovsr.png",
//     position: "left" as const
//   },
//   {
//     id: 3,
//     title: "BS. Lê Thị Minh Thùy",
//     description: "Bác sĩ Răng Hàm Mặt – Chuyên sâu Phục hình thẩm mỹ mặt dán sứ (Veneer)" +
//           ". Công tác tại Bệnh viện Đa khoa Khu vực Gò Công" +
//           "\n\nBác sĩ Lê Thị Minh Thùy là bác sĩ Răng Hàm Mặt trẻ đầy nhiệt huyết, hiện đang công tác tại Bệnh viện Đa khoa Khu vực Gò Công từ năm 201",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/BS_Thùy_Zoomed_vdafi9.png",
//     position: "right" as const
//   },
//   {
//     id: 4,
//     title: "BSCKI. Phạm Nguyễn Tiên Phước",
//     description: "Chuyên khoa Răng Hàm Mặt – Cấy ghép Implant nha khoa" +
//       "\n\nVới hơn 6 năm kinh nghiệm trong lĩnh vực Răng Hàm Mặt, BSCKI. Phạm Nguyễn Tiên Phước là một trong những bác sĩ trẻ giàu nhiệt huyết, không ngừng học hỏi và nâng cao tay nghề để mang lại trải nghiệm điều trị tốt nhất cho bệnh nhân.",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/BS_Ph%C6%B0%E1%BB%9Bc_sf2nv6.png",
//     position: "left" as const
//   }
// ];

const Doctor = () => {
  const doctors = usePage().props.doctors as DoctorType[];

  const CoreValueItem = ({ value, index }: { value: typeof doctors[0], index: number }) => {
    if (index % 2 === 0) {
      return (
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">{value.name}</h3>
            <div className="w-24 h-1 bg-emerald-600 mb-8"></div>
            <p className="text-lg text-gray-600 leading-relaxed whitespace-pre-wrap">{value.description}</p>
          </div>
          <div className="relative">
            <img 
              src={value.image} 
              alt={value.name}
              className="w-full h-[480px] object-contain rounded-2xl bg-background"
            />
            <div className="absolute -inset-4 rounded-2xl -z-10 opacity-30"></div>
          </div>
        </div>
      );
    } else {
      return (
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="relative order-2 lg:order-1">
            <img 
              src={value.image} 
              alt={value.name}
              className="w-full h-[480px] object-contain rounded-2xl bg-background"
            />
            <div className="absolute -inset-4 rounded-2xl -z-10 opacity-30"></div>
          </div>
          <div className="order-1 lg:order-2">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">{value.name}</h3>
            <div className="w-24 h-1 bg-emerald-600 mb-8"></div>
            <p className="text-lg text-gray-600 leading-relaxed">{value.description}</p>
          </div>
        </div>
      );
    }
  };

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="space-y-16">
            {doctors.map((value, index) => (
                <CoreValueItem key={value.id} value={value} index={index} />
            ))}
        </div>
      </div>
    </section>
  );
};

export default Doctor;
