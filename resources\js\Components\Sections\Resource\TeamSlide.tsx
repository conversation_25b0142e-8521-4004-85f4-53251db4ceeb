
import { Card, CardContent } from "@/Components/ui/card";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@/Components/ui/carousel";
import { useEffect, useState } from "react";
import { usePage } from "@inertiajs/react";
import { Team as TeamType, Title } from "@/types";

// const teamMembers = [
//   {
//     name: "CSKH",
//     qualifications: "Giám đốc chuyên môn tại Nha khoa 246",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/CSKH_1_byljll.jpg",
//     details: "Chứng chỉ Cấy ghép implant và Chỉnh hình răng hàm mặt – ĐH Y dược Tp Hồ Chí Minh."
//   },
//   {
//     name: "CSKH",
//     qualifications: "Gi<PERSON><PERSON> đốc chuyên môn tại Nha khoa 246",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/CSKH_3_tvmaoq.jpg",
//     details: "Chứng chỉ Cấy ghép implant và Chỉnh hình răng hàm mặt – ĐH Y dược Tp Hồ Chí Minh."
//   },
//   {
//     name: "CSKH",
//     qualifications: "Giám đốc chuyên môn tại Nha khoa 246",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/CSKH2_jnd9py.jpg",
//     details: "Chứng chỉ Cấy ghép implant và Chỉnh hình răng hàm mặt – ĐH Y dược Tp Hồ Chí Minh."
//   },
//   {
//     name: "Kỹ thuật viên Labo",
//     qualifications: "Giám đốc chuyên môn tại Nha khoa 246",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/tr%E1%BB%A3_th%E1%BB%A7_1_wipcub.jpg",
//     details: "Chứng chỉ Cấy ghép implant và Chỉnh hình răng hàm mặt – ĐH Y dược Tp Hồ Chí Minh."
//   },
//   {
//     name: "Kỹ thuật viên Labo",
//     qualifications: "Giám đốc chuyên môn tại Nha khoa 246",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/tr%E1%BB%A3_th%E1%BB%A7_2_uocn1b.jpg",
//     details: "Chứng chỉ Cấy ghép implant và Chỉnh hình răng hàm mặt – ĐH Y dược Tp Hồ Chí Minh."
//   },
//   {
//     name: "Trợ thủ",
//     qualifications: "Giám đốc chuyên môn tại Nha khoa 246",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/tr%E1%BB%A3_th%E1%BB%A7_3_w6in4b.jpg",
//     details: "Chứng chỉ Cấy ghép implant và Chỉnh hình răng hàm mặt – ĐH Y dược Tp Hồ Chí Minh."
//   },
//   {
//     name: "Trợ thủ",
//     qualifications: "Giám đốc chuyên môn tại Nha khoa 246",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/v1752943760/Tr%E1%BB%A3_th%E1%BB%AD_4_edit_ullgom.png",
//     details: "Chứng chỉ Cấy ghép implant và Chỉnh hình răng hàm mặt – ĐH Y dược Tp Hồ Chí Minh."
//   }
// ];

const TeamSlide = () => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);

  const teamTitle = usePage().props.teamTitle as Title;
  const teamMembers = usePage().props.teams as TeamType[];

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  const goToSlide = (index: number) => {
    if (api) {
      api.scrollTo(index);
    }
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl max-w-3xl mx-auto font-bold text-gray-900 mb-4">
            {teamTitle?.main_title ?? "Gặp Gỡ Đội Ngũ Trợ Thủ Và Chăm Sóc Khách Hàng Tại Nha Khoa 246"}
          </h2>
          {/* <div className="w-24 h-1 bg-emerald-600 mx-auto mb-8"></div> */}
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {teamTitle?.subtitle ?? "Đội ngũ trợ thủ tại Nha Khoa 246 là một trong những yếu tố tạo nên nền tảng vận hành vững chắc cho toàn bộ hệ thống điều trị, không chỉ hỗ trợ bác sĩ trong kỹ thuật chuyên môn một cách nhanh nhạy và chính xác, mà còn chuyên nghiệp trong tác phong, tận tâm trong chăm sóc, và khả năng giao tiếp khéo léo với khách hàng."}
          </p>
        </div>
        
        <div className="relative max-w-6xl mx-auto">
          <Carousel
            setApi={setApi}
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent className="-ml-2 md:-ml-4">
              {teamMembers.map((member, index) => (
                <CarouselItem key={index} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                  <Card className="bg-white hover:shadow-lg transition-shadow duration-300 overflow-hidden h-full">
                    <CardContent className="p-0">
                      <div className="relative overflow-hidden">
                        <img 
                          src={member.image}
                          alt={member.position}
                          className="w-full h-[480px] object-cover transition-transform duration-300 hover:scale-105"
                        />
                      </div>
                      {/* <div className="p-6 space-y-4">
                        <div>
                          <h3 className="text-xl font-bold text-gray-900 text-center">
                            {member.position}
                          </h3>
                          <p className="text-emerald-600 font-semibold text-sm mb-3">
                            {member.qualifications}
                          </p>
                        </div>
                        <p className="text-gray-600 text-sm leading-relaxed whitespace-pre-wrapne">
                          {member.details}
                        </p>
                      </div> */}
                    </CardContent>
                  </Card>
                </CarouselItem>
              ))}
            </CarouselContent>
            {/* <CarouselPrevious className="left-4 hidden" />
            <CarouselNext className="right-4 hidden" /> */}
          </Carousel>

          {/* Slide Indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {Array.from({ length: count }).map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === current - 1
                    ? 'bg-emerald-600' 
                    : 'bg-gray-400 hover:bg-emerald-400'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TeamSlide;
