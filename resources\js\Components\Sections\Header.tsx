import { But<PERSON> } from "@/Components/ui/button";
import { Link } from "@inertiajs/react";
import { usePage } from "@inertiajs/react";
import { CommonInfo } from "@/types";
import { Sheet, SheetContent, SheetTrigger } from "@/Components/ui/sheet";
import { Menu } from "lucide-react";
import { Separator } from "../ui/separator";

const Header = () => {
  const commonInfo = usePage().props.commonInfo as CommonInfo;
  const logoLink = commonInfo?.logo_link ?? "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/logo_xorkjq.jpg";

  return (
      // <nav className="fixed top-0 w-full z-50 bg-white/90 backdrop-blur-sm shadow-sm">
      <nav className="fixed top-0 w-full z-50 bg-white/70 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Link href="/">
                <div className="w-12 h-12 rounded-full flex items-center justify-center">
                  <img src={logoLink} alt="logo" className="rounded-full" />
                </div>
              </Link>
              <div className="flex flex-col">
                <span className="font-bold font-panteka text-gray-900 uppercase">{commonInfo?.name ?? "Nha Khoa 246"}</span>
                <span className="font-bold font-panteka text-gray-900 uppercase">{commonInfo?.name2 ?? "USmile Dental"}</span>
                {/* <span className="font-bold text-white">NHA KHOA 246</span>
                <span className="font-bold text-white">USMILE DENTAL</span> */}
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-4 lg:space-x-8">
              <Link href="/" className="text-gray-900 hover:text-emerald-600 transition-colors">Trang Chủ</Link>
              <Link href="/about" className="text-gray-900 hover:text-emerald-600 transition-colors">Giới Thiệu</Link>
              <Link href="/resource" className="text-gray-900 hover:text-emerald-600 transition-colors">Đội Ngũ Nha Khoa</Link>
              <Link href="/contact"><Button>Đặt Lịch Ngay</Button></Link>

              {/* <Link href="/" className="text-white/90 hover:text-white transition-colors">Trang Chủ</Link>
              <Link href="/about" className="text-white/90 hover:text-white transition-colors">Giới Thiệu</Link>
              <Link href="/resource" className="text-white/90 hover:text-white transition-colors">Nhân Sự</Link>
              <Button className="border-white/20">Đặt Lịch Ngay</Button> */}
            </div>

            {/* Mobile Menu */}
            <div className="md:hidden">
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="icon" className="text-gray-800 hover:text-emerald-600 hover:bg-gray-100">
                    <Menu className="h-6 w-6" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white !max-w-[280px]">
                  <div className="flex flex-col space-y-4 mt-4">
                    <div className="flex items-center space-x-4 mb-4">
                      <Link href="/">
                        <div className="w-12 h-12 rounded-full flex items-center justify-center">
                          <img src={logoLink} alt="logo" className="rounded-full" />
                        </div>
                      </Link>
                      <div className="flex flex-col">
                        <span className="font-bold font-panteka text-gray-900 uppercase">{commonInfo?.name ?? "Nha Khoa 246"}</span>
                        <span className="font-bold font-panteka text-gray-900 uppercase">{commonInfo?.name2 ?? "USmile Dental"}</span>
                      </div>
                    </div>
                    
                    <Link href="/" className="text-gray-900 hover:text-emerald-600 transition-colors">Trang Chủ</Link>
                    <Link href="/about" className="text-gray-900 hover:text-emerald-600 transition-colors">Giới Thiệu</Link>
                    <Link href="/resource" className="text-gray-900 hover:text-emerald-600 transition-colors">Đội Ngũ Nha Khoa</Link>
                          
                    <Separator className="bg-gray-200 w-3/4" />
                    
                    <Link href="/contact"><Button>Đặt Lịch Ngay</Button></Link>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </nav>
  );
};

export default Header;
