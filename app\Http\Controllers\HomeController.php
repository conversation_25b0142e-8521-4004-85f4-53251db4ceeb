<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use App\Models\CommonInfo;
use App\Models\CustomerReview;
use App\Models\HomeHero;
use App\Models\SectionTitle;
use Illuminate\Http\Request;
use Inertia\Inertia;

class HomeController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Admin/TrangChu', [
            'homeHero' => HomeHero::first(),
            'activitiesTitle' => SectionTitle::where('section_name', 'activities')->first(),
            'activities' => Activity::all(),
            'customerReviewTitle' => SectionTitle::where('section_name', 'customer_reviews')->first(),
            'customerReviews' => CustomerReview::all(),
            'commonInfo' => CommonInfo::first(),
        ]);
    }
}
