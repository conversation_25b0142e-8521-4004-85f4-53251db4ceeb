import { <PERSON>, CardContent, <PERSON>Header, CardTitle, CardDescription } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Image, Save, Phone, Mail, MapPin, Link } from "lucide-react";
import { useEffect, useState } from "react";
import { Input } from "@/Components/ui/input";
import { toast } from "sonner";
import { router, usePage } from "@inertiajs/react";
import { CommonInfo } from "@/types";

const ContactInfo = () => {
    const commonInfo = usePage().props.commonInfo as CommonInfo;

    const [contactData, setContactData] = useState({
      id: commonInfo?.id ?? 0,
      phone: commonInfo?.phone ?? "0273.6588.988",
      phone2: commonInfo?.phone2 ?? "0939.809.246",
      location: commonInfo?.location ?? "166 Trương Đ<PERSON>nh, khu <PERSON>h<PERSON> 3, <PERSON><PERSON>, tỉnh <PERSON><PERSON>", 
      email: commonInfo?.email ?? "<EMAIL>",
      google_map_link: commonInfo?.google_map_link ?? "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3924.8109477379166!2d106.67020109678955!3d10.356996300000015!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31754f002909cebd%3A0x48e8296554c936ed!2sNha%20Khoa%20246%20-USmile%20Dental%20Office!5e0!3m2!1svi!2s!4v1752676492429!5m2!1svi!2s",
      name: commonInfo?.name ?? "Nha Khoa 246",
      name2: commonInfo?.name2 ?? "USmile Dental",
      logo_link: commonInfo?.logo_link ?? "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/logo_xorkjq.jpg",
      service_list: commonInfo?.service_list ?? [
        'Nhổ Răng Khôn',
        'Chỉnh Nha',
        'Trồng Răng Trên Implant',
        'Nha Khoa Thẩm Mỹ'
      ],
    });

    useEffect(() => {
        if (!commonInfo) return;

        setContactData(commonInfo);
    }, [commonInfo]);

    const handleSave = () => {
        if (contactData.id !== 0) {
            router.put(
                route('common-infos.update', contactData.id), 
                contactData, 
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
            
        } else {
            router.post(route('common-infos.store'), 
                contactData,
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
        }
    };
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Mail className="w-5 h-5 mr-2" />
            Thông tin liên hệ
          </CardTitle>
          <CardDescription>Quản lý thông tin liên hệ</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-1 gap-6">
            <div className="space-y-4">
              <h4 className="font-medium">Số Điện Thoại, Email, Địa Chỉ</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Phone className="w-4 h-4" />
                  <div className="flex flex-col md:flex-row w-full gap-2">
                      <Input
                          value={contactData.phone}
                          onChange={(e) => setContactData({
                              ...contactData, 
                              phone: e.target.value
                          })}
                          placeholder="Số điện thoại 1"
                      />
                      <Input
                          value={contactData.phone2}
                          onChange={(e) => setContactData({
                              ...contactData, 
                              phone2: e.target.value
                          })}
                          placeholder="Số điện thoại 2"
                      />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="w-4 h-4" />
                  <Input
                    value={contactData.email}
                    onChange={(e) => setContactData({
                      ...contactData, 
                      email: e.target.value
                    })}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4" />
                  <Input
                    value={contactData.location}
                    onChange={(e) => setContactData({
                      ...contactData, 
                      location: e.target.value
                    })}
                  />
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium">Link Địa chỉ Google Map</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Link className="w-4 h-4" />
                  <Input
                    value={contactData.google_map_link}
                    onChange={(e) => setContactData({
                      ...contactData, 
                      google_map_link: e.target.value
                    })}
                  />
                  </div>
                  <div className="w-full h-60 rounded-lg overflow-hidden">
                      <iframe
                          src={contactData.google_map_link}
                          width="100%"
                          height="100%"
                          style={{ border: 0 }}
                          allowFullScreen
                          loading="lazy"
                          referrerPolicy="no-referrer-when-downgrade"
                          title="Nha khoa 246"
                      />
                  </div>
                </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button onClick={() => handleSave()}>
              <Save className="w-4 h-4" />
              Lưu thay đổi
            </Button>
          </div>
        </CardContent>
      </Card>
    )
}

export default ContactInfo;