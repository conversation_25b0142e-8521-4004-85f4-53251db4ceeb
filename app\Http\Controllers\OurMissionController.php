<?php

namespace App\Http\Controllers;

use App\Models\OurMission;
use App\Models\SectionTitle;
use Illuminate\Http\Request;

class OurMissionController extends Controller
{
    public function store(Request $request)
    {
        SectionTitle::create($request->all());
        $this->handleOurMissions($request);
        return back();
    }
    public function update(Request $request, SectionTitle $sectionTitle)
    {
        $sectionTitle->update($request->all());
        $this->handleOurMissions($request);
        return back();
    }
    public function handleOurMissions(Request $request)
    {
        $ourMissions = $request['doctors'];

        OurMission::first()
            ? OurMission::first()->update(['doctors' => $ourMissions])
            : OurMission::create(['doctors' => $ourMissions]);
    }
}
