<?php

namespace App\Http\Controllers;

use App\Models\ResourceHero;
use Illuminate\Http\Request;

class ResourceHeroController extends Controller
{
    public function store(Request $request)
    {
        ResourceHero::create($request->all());
        return back();
    }
    public function update(Request $request, ResourceHero $resourceHero)
    {
        $resourceHero->update($request->all());
        return back();
    }
}
