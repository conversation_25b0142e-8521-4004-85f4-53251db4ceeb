import { SidebarTrigger } from '@/Components/ui/sidebar';
import { type BreadcrumbItem as BreadcrumbItemType } from '@/types';
import { AppSidebarBreadcrumbs } from './app-sidebar-breadcrumbs';
import { Separator } from '@/Components/ui/separator';

export function AppSidebarHeader({ breadcrumbs = [] }: { breadcrumbs?: BreadcrumbItemType[] }) {
    return (
         <header className="flex h-16 shrink-0 items-center gap-2 border-b">
            <div className="flex items-center gap-2 px-4">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="mr-2 h-4" />
                <AppSidebarBreadcrumbs breadcrumbs={breadcrumbs} />
            </div>
        </header>
    );
}
