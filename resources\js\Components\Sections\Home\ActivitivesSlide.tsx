import { Card, CardContent } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Play, Calendar, Users } from "lucide-react";
import MediaModal from "@/Components/MediaModal";
import { useState, useEffect } from "react";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@/Components/ui/carousel";
import { usePage } from "@inertiajs/react";
import { Activity, Title } from "@/types";

// const activities = [
//   {
//     id: 1,
//     sortTitle: "VỀ NHA KHOA 246",
//     title: "VỀ NHA KHOA 246",
//     sortDescription: "1. Hành trình phát triển – Đầu tư hiện đại.\n"
//       + "Nha khoa 246 – USmile Dental Office tự hào là một trong những Trung tâm trồng răng thẩm mỹ trên Implant và Niềng răng hàng đầu tại thành phố Gò Công",
//     description: "1. <PERSON><PERSON><PERSON> trình phát triển – <PERSON><PERSON><PERSON> tư hiện đại.\n\n"
//       + "Nha khoa 246 – USmile Dental Office tự hào là một trong những Trung tâm trồng răng thẩm mỹ trên Implant và Niềng răng hàng đầu tại thành phố Gò Công, Tiền Giang, với gần 10 năm phát triển bền vững, không ngừng đổi mới vì sức khỏe răng miệng của cộng đồng với những cột mốc đáng nhớ:\n"
//       + "● Tiền thân là Nha khoa 246 ra đời từ năm 2015 với quy mô 01 ghế nha với mong muốn đơn giản: mang đến nụ cười đến mọi nhà\n"
//       + "● Năm 2018, thành lập công ty TNHH Usmile Dental với số lượng 03 ghế nha, từng bước mở rộng quy mô hoạt động. Lượng khách hàng tin tưởng và sử dụng dịch vụ ngày càng tăng. Để đáp ứng nhu cầu và trải nghiệm khách hàng, năm 2022 phòng khám lắp đặt thêm 03 ghế nha và cập nhật nhiều trang thiết bị phục vụ công tác điều trị\n"
//       + "● Ngày 12.04.2025: Nha khoa 246 – Usmile Dental Office chính thức cắt băng khánh thành, ra mắt cơ sở mới hiện đại với 10 ghế nha và hệ thống trang thiết bị công nghệ cao. Với tầm nhìn dài hạn, Nha khoa liên tục đầu tư vào hệ thống cơ sở vật chất và công nghệ.\n\n"
//       + "Không chỉ đầu tư công nghệ, đội ngũ gồm 04 bác sĩ chuyên môn cao được đào tạo bài bản, luôn cập nhật kiến thức và công nghệ điều trị mới nhất. Mỗi bác sĩ tại đây không chỉ là người điều trị mà còn là người đồng hành, lắng nghe và thấu hiểu khách hàng.\n\n"
//       + "2. Giá trị khác biệt của Nha khoa 246\n\n"
//       + "“Trung thực – Tận tâm – Đồng cảm – Bền vững” là giá trị cốt lõi của Nha khoa chúng tôi. Chính giá trị này đã tạo nên sự khác biệt và tạo dựng niềm tin với khách hàng trong khu vực và đặc biệt là khách hàng Việt Kiều\n"
//       + "Tại Nha khoa 246, mọi quyết định và hành động đều được xây dựng trên nền tảng của 4 giá trị cốt lõi – kim chỉ nam cho định hướng phát triển và cách chúng tôi phục vụ khách hàng mỗi ngày.\n\n"
//       + "2.1 Trung thực\n"
//       + "Chúng tôi cam kết minh bạch trong mọi thông tin điều trị, chi phí và lựa chọn phương pháp. Khách hàng luôn được tư vấn rõ ràng, trung thực về tình trạng răng miệng và các giải pháp phù hợp – không “vẽ bệnh”, không chiêu trò.\n\n"
//       + "2.2 Tận tâm\n"
//       + "Mỗi ca điều trị, dù đơn giản hay phức tạp, đều được thực hiện với sự chăm sóc kỹ lưỡng và tinh thần trách nhiệm cao nhất. Chúng tôi xem sự hài lòng của khách hàng là thành công của chính mình.\n\n"
//       + "2.3 Đồng cảm\n"
//       + "Chúng tôi thấu hiểu rằng mỗi khách hàng đều mang theo những nỗi lo riêng về sức khỏe, thời gian và chi phí. Đội ngũ bác sĩ và nhân viên tại Nha khoa 246 luôn lắng nghe, tôn trọng và đồng hành cùng khách hàng trong từng bước điều trị – như người thân trong gia đình.\n\n"
//       + "2.4 Bền vững\n"
//       + "Nha khoa 246 không chỉ hướng đến kết quả trước mắt, mà còn đặt trọng tâm vào hiệu quả lâu dài: từ việc lựa chọn vật liệu điều trị chất lượng, thiết bị hiện đại, đến việc theo dõi sau điều trị và giáo dục chăm sóc răng miệng. Chúng tôi mong muốn tạo ra nụ cười khỏe đẹp bền vững, chứ không phải giải pháp tạm thời.",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/NIK_3192_e3tl8z.jpg",
//     type: "image" as const,
//     date: "Tháng Tư 2025",
//     participants: "4 bác sĩ chuyên khoa",
//     media_url: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/NIK_3192_e3tl8z.jpg"
//   },
//   {
//     id: 2,
//     sortTitle: "NGÀY HỘI CHỈNH NHA",
//     title: "NGÀY HỘI CHỈNH NHA – LAN TỎA NỤ CƯỜI TỰ TIN CÙNG NHA KHOA 246",
//     sortDescription: "Không chỉ là một sự kiện, Ngày hội Chỉnh Nha 22/6 là hành trình đánh thức khát khao thay đổi – nơi mỗi người tham dự được nhìn thấy hình ảnh phiên bản tốt hơn của chính mình, bắt đầu từ một nụ cười đều đẹp, tự tin.",
//     description: "Không chỉ là một sự kiện, Ngày hội Chỉnh Nha 22/6 là hành trình đánh thức khát khao thay đổi – nơi mỗi người tham dự được nhìn thấy hình ảnh phiên bản tốt hơn của chính mình, bắt đầu từ một nụ cười đều đẹp, tự tin.\n\n"
//       + "Với sứ mệnh “Kiến tạo nụ cười – Truyền cảm hứng sống tích cực”, ngày hội ra đời nhằm:\n"
//       + "● Giúp khách hàng hiểu rõ hơn về lợi ích thẩm mỹ, sức khỏe và tâm lý của niềng răng\n"
//       + "● Tạo môi trường gần gũi để mọi người được lắng nghe, được chia sẻ và được tư vấn cá nhân hóa bởi bác sĩ chuyên chỉnh nha\n"
//       + "● Giúp mọi người tin rằng: ai cũng có thể bắt đầu hành trình thay đổi từ hôm nay\n\n"
//       + "Ngày hội diễn ra tại Nha khoa 246 trong không gian ấm cũng và chuyên nghiệp với hàng loạt trải nghiệm ý nghĩa:\n"
//       + "● Khám & chụp phim X-quang hoàn toàn miễn phí với trang thiết bị hiện đại\n"
//       + "● Lắng nghe chia sẻ từ bác sĩ về các phương pháp niềng răng \n"
//       + "● Tư vấn riêng 1:1 cho từng khách hàng, giúp xác định tình trạng lệch lạc và phác đồ phù hợp\n"
//       + "● Gặp gỡ những “người thật, kết quả thật” – khách hàng đang niềng hoặc đã tháo niềng, truyền cảm hứng mạnh mẽ\n"
//       + "_________________________________________________________________________________________________________________________\n\n"
//       + "“Lâu nay tôi nghĩ niềng răng là chuyện xa xôi… Nhưng sau hôm nay, tôi thấy mình hoàn toàn có thể bắt đầu – Nha khoa hỗ trợ trả góp, có bác sĩ đồng hành, có kế hoạch rõ ràng, và có cả động lực từ những nụ cười đẹp trước mắt.”\n"
//       + "— Một khách hàng chia sẻ sau khi tham dự",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/PHPA6419A_ccfyfr.jpg",
//     type: "image" as const,
//     date: "Tháng Sáu 2025",
//     participants: "200+ người tham gia",
//     media_url: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/PHPA6419A_ccfyfr.jpg"
//   },
//   {
//     id: 3,
//     sortTitle: "NHA KHOA HỌC ĐƯỜNG",
//     title: "Hoạt động cộng đồng – Gieo nụ cười, lan tỏa yêu thương",
//     sortDescription: "Nha khoa 246 đồng hành cùng học sinh Trường THPT Trương Định và Bình Đông\n"
//       + "Trong tháng 4 và tháng 5 năm 2025, Nha khoa 246 – Usmile Dental Office đã vinh dự đồng hành cùng 2 trường THPT Trương Định và Bình Đông",
//     description: "Nha khoa 246 đồng hành cùng học sinh Trường THPT Trương Định và THPT Bình Đông\n\n"
//       + "Trong tháng 4 và tháng 5 năm 2025, Nha khoa 246 – Usmile Dental Office đã vinh dự đồng hành cùng 2 trường THPT Trương Định và Bình Đông để tổ chức các buổi sinh hoạt “Nha khoa học đường” ý nghĩa, nhằm nâng cao nhận thức về sức khỏe răng miệng cho học sinh nói riêng và cộng đồng nói chung.\n\n"
//       + "Tại buổi sinh hoạt, đại diện nha khoa 246 đã trực tiếp giao lưu, chia sẻ kiến thức chăm sóc răng miệng đúng cách và gởi đến các các ưu đãi đặc biệt cho các em học sinh tham dự.\n\n"
//       + "Một số nội dung nổi bật trong chương trình:\n"
//       + "● Ký kết hợp tác đồng hành lâu dài giữa Nha khoa 246 và nhà trường\n"
//       + "● Tư vấn – Hướng dẫn chăm sóc răng miệng học đường\n"
//       + "● Dành tặng các voucher ưu đãi dịch vụ nha khoa cho học sinh\n\n"
//       + "Buổi sinh hoạt không chỉ mang lại kiến thức bổ ích, mà còn giúp các bạn học sinh hiểu rõ hơn về vai trò của sức khỏe răng miệng trong học tập, giao tiếp và cuộc sống hàng ngày. Đây cũng là dịp để Nha khoa 246 thể hiện cam kết gắn bó lâu dài với cộng đồng giáo dục địa phương.\n\n"
//       + "Đặc biệt vinh dự khi Nha khoa 246 được đồng hành cùng Nhà trường để trao tặng nhiều suất học bổng giá trị nhằm khuyến khích, động viên tinh thần học tập của các em\n\n"
//       + "Qua các hoạt động cộng đồng trên có thể thấy, Nha khoa 246 không chỉ chú trọng chuyên môn và chất lượng dịch vụ, mà còn luôn dành sự quan tâm đặc biệt đến các hoạt động vì cộng đồng và thế hệ trẻ.",
//     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/Sinh_ho%E1%BA%A1t_2_b%C3%ACnh_%C4%91%C3%B4ng_a2ofhs.jpg",
//     type: "image" as const,
//     date: "Tháng Một 2024",
//     participants: "500+ học sinh",
//     media_url: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/Sinh_ho%E1%BA%A1t_2_b%C3%ACnh_%C4%91%C3%B4ng_a2ofhs.jpg",
//     thumbnailUrl: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/Sinh_ho%E1%BA%A1t_2_b%C3%ACnh_%C4%91%C3%B4ng_a2ofhs.jpg"
//   },
// ];

const ActivitiesSlide = () => {
  const activities = usePage().props.activities as Activity[];
  const activitiesTitle = usePage().props.activitiesTitle as Title;

  const [selectedMedia, setSelectedMedia] = useState<{
      type: "image" | "video";
      title: string;
      description: string;
      media_url: string;
      image?: string;
      // thumbnailUrl?: string;
  } | null>(null);

  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  const handleMediaClick = (activity: typeof activities[0]) => {
    setSelectedMedia({
      type: activity.type,
      title: activity.longtitle,
      description: activity.long_description,
      media_url: activity.media_url,
      image: activity.image,
      // thumbnailUrl: activity.thumbnailUrl
    });
  };

  const goToSlide = (index: number) => {
    if (api) {
      api.scrollTo(index);
    }
  };

  return (
    <>
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{activitiesTitle?.main_title ?? "Những Hoạt Động Nổi Bật Tại Nha Khoa 246"}</h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                 {activitiesTitle?.subtitle ?? "Khám phá các hoạt động và dịch vụ nổi bật tại nha khoa của chúng tôi – từ chăm sóc răng miệng tổng quát đến các giải pháp thẩm mỹ hiện đại. Nha Khoa 246 luôn đặt chất lượng điều trị và sự hài lòng của bạn lên hàng đầu." }
              </p>
          </div>

          {/* Carousel Container */}
          <div className="relative max-w-6xl mx-auto">
            <Carousel
              setApi={setApi}
              opts={{
                align: "start",
                loop: true,
              }}
              className="w-full"
            >
              <CarouselContent className="-ml-2 md:-ml-4">
                {activities.map((activity) => (
                  <CarouselItem key={activity.id} className="pl-2 md:pl-4 md:basis-1/2">
                    <Card className="group hover:shadow-xl transition-all duration-300 overflow-hidden h-full">
                      <div className="relative cursor-pointer" onClick={() => handleMediaClick(activity)}>
                        <img 
                          src={activity.image} 
                          alt={activity.shorttitle}
                          className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                      
                      <CardContent className="p-6">
                        <h3 className="text-xl font-semibold text-gray-900 mb-3">{activity.shorttitle}</h3>
                        <p className="text-gray-600 mb-4 whitespace-pre-wrap leading-relaxed">{activity.short_description}</p>
                        
                        <div className="flex flex-wrap gap-4 items-center justify-between text-sm text-gray-500 mb-4">
                          <div className="flex items-center">
                            <Calendar className="w-4 h-4 mr-1" />
                            {activity.date}
                          </div>
                          <div className="flex items-center">
                            <Users className="w-4 h-4 mr-1" />
                            {activity.participants}
                          </div>
                        </div>
                        
                        <Button 
                          variant="landing_outline" 
                          className="w-full group-hover:bg-emerald-600 group-hover:text-white transition-colors duration-300"
                          onClick={() => handleMediaClick(activity)}
                        >
                          Xem Thêm
                        </Button>
                      </CardContent>
                    </Card>
                  </CarouselItem>
                ))}
              </CarouselContent>
              {/* <CarouselPrevious className="left-4" />
              <CarouselNext className="right-4" /> */}
            </Carousel>

            {/* Slide Indicators */}
            <div className="flex justify-center mt-8 space-x-2">
              {Array.from({ length: count }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === current - 1
                      ? 'bg-emerald-600' 
                      : 'bg-gray-400 hover:bg-emerald-400'
                  }`}
                />
              ))}
            </div>
          </div>
            
        </div>
      </section>
      
      <MediaModal
          isOpen={!!selectedMedia}
          onClose={() => setSelectedMedia(null)}
          type={selectedMedia?.type || "image"}
          title={selectedMedia?.title || ""}
          description={selectedMedia?.description}
          media_url={selectedMedia?.media_url || ""}
          image={selectedMedia?.image}
          // thumbnailUrl={selectedMedia?.thumbnailUrl}
      />
    </>
  );
};

export default ActivitiesSlide;