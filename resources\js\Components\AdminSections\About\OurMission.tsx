import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Image, Save } from "lucide-react";
import { useEffect, useState } from "react";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import { Textarea } from "@/Components/ui/textarea";
import { Separator } from "@/Components/ui/separator";
import { toast } from "sonner";
import { router, usePage } from "@inertiajs/react";
import { Title } from "@/types";

const OurMission = () => {
    const ourMissionTitle = usePage().props.ourMissionTitle as Title;
    const ourMissions = usePage().props.ourMissions as string[];

    const [ourMissionData, setOurMissionData] = useState({
        id: ourMissionTitle?.id ?? 0,
        main_title: ourMissionTitle?.main_title ?? "Your Smile,",
        subtitle: ourMissionTitle?.subtitle ?? "Experience exceptional dental care with compassionate professionals dedicated to your comfort and oral health.",
        section_name: "our_missions",
        doctors: ourMissions.length > 0 ? ourMissions : [
            "", "", "", ""
        ]
    });

    useEffect(() => {
        if (!ourMissionTitle) return;

        setOurMissionData({...ourMissionTitle, doctors: ourMissions.length > 0 ? ourMissions : [
            "", "", "", ""
        ]});
    }, [ourMissionTitle, ourMissions]);

    const handleSave = () => {
        if (ourMissionData.id !== 0) {
            router.put(
                route('our-missions.update', ourMissionData.id), 
                ourMissionData, 
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
            
        } else {
            router.post(route('our-missions.store'), 
                ourMissionData,
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
        }
    };
    
    return (
        <Card>
            <CardHeader>
            <CardTitle className="flex items-center">
                <Image className="w-5 h-5 mr-2" />
                Sứ mệnh của chúng tôi
            </CardTitle>
            <CardDescription>Quản lý nội dung và hình ảnh sứ mệnh của chúng tôi</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
            <div className="grid md:grid-cols-1 gap-6">
                <div className="space-y-4">
                    <div>
                        <Label htmlFor="main_title">Tiêu đề</Label>
                        <Input
                        id="main_title"
                        value={ourMissionData.main_title}
                        onChange={(e) => setOurMissionData({...ourMissionData, main_title: e.target.value})}
                        />
                    </div>
                    <div>
                        <Label htmlFor="subtitle">Phụ đề</Label>
                        <Textarea
                        id="subtitle"
                        value={ourMissionData.subtitle}
                        onChange={(e) => setOurMissionData({...ourMissionData, subtitle: e.target.value})}
                        rows={3}
                        />
                    </div>
                </div>
            </div>

            <Separator />
            
            <div>
                <h4 className="font-medium mb-4">Danh sách hình ảnh của các bác sĩ</h4>
                <div className="space-y-3">
                    {ourMissionData.doctors.map((image, index) => (
                        <div key={index} className="flex items-center space-x-2">
                            <span className="text-sm text-muted-foreground w-8">#{index + 1}</span>
                            <Input
                                value={image}
                                onChange={(e) => {
                                const updated = [...ourMissionData.doctors];
                                updated[index] = e.target.value;
                                setOurMissionData({...ourMissionData, doctors: updated});
                                }}
                                placeholder="Link ảnh"
                            />
                        </div>
                    ))}
                    {/* <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground w-8">#1</span>
                        <Input
                            value={ourMissionData.doctor1}
                            onChange={(e) => {
                                setOurMissionData({...ourMissionData, doctor1: e.target.value});
                            }}
                            placeholder="Link ảnh"
                        />
                    </div>
                    <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground w-8">#2</span>
                        <Input
                            value={ourMissionData.doctor2}
                            onChange={(e) => {
                                setOurMissionData({...ourMissionData, doctor2: e.target.value});
                            }}
                            placeholder="Link ảnh"
                        />
                    </div>
                    <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground w-8">#3</span>
                        <Input
                            value={ourMissionData.doctor3}
                            onChange={(e) => {
                                setOurMissionData({...ourMissionData, doctor3: e.target.value});
                            }}
                            placeholder="Link ảnh"
                        />
                    </div>
                    <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground w-8">#4</span>
                        <Input
                            value={ourMissionData.doctor4}
                            onChange={(e) => {
                                setOurMissionData({...ourMissionData, doctor4: e.target.value});
                            }}
                            placeholder="Link ảnh"
                        />
                    </div> */}
                    {/* {ourMissionData.background_images.map((image, index) => (
                        <div key={index} className="flex items-center space-x-2">
                            <span className="text-sm text-muted-foreground w-8">#{index + 1}</span>
                            <Input
                                value={image}
                                onChange={(e) => {
                                const updated = [...ourMissionData.background_images];
                                updated[index] = e.target.value;
                                setOurMissionData({...ourMissionData, background_images: updated});
                                }}
                                placeholder="Image URL"
                            />
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                const updated = ourMissionData.background_images.filter((_, i) => i !== index);
                                setOurMissionData({...ourMissionData, background_images: updated});
                                }}
                            >
                                <Trash2 className="w-4 h-4" />
                            </Button>
                        </div>
                    ))}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setOurMissionData({
                        ...ourMissionData, 
                        background_images: [...ourMissionData.background_images, ""]
                        })}
                    >
                        <Plus className="w-4 h-4" />
                        Thêm hình ảnh
                    </Button> */}
                </div>
            </div>

            <div className="flex justify-end">
                <Button onClick={() => handleSave()}>
                    <Save className="w-4 h-4" />
                    Lưu thay đổi
                </Button>
            </div>
            </CardContent>
        </Card>
    )
}

export default OurMission;