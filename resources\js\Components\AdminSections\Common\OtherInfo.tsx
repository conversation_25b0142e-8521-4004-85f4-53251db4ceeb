import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title, CardDescription } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Image, Plus, Save, Settings, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { Input } from "@/Components/ui/input";
import { toast } from "sonner";
import { router, usePage } from "@inertiajs/react";
import { CommonInfo } from "@/types";

const OtherInfo = () => {
    const commonInfo = usePage().props.commonInfo as CommonInfo;

    const [otherInfoData, setOtherInfoData] = useState({
      id: commonInfo?.id ?? 0,
      phone: commonInfo?.phone ?? "0273.6588.988",
      phone2: commonInfo?.phone2 ?? "0939.809.246",
      location: commonInfo?.location ?? "166 Tr<PERSON>ơng Đ<PERSON>nh, khu <PERSON>h<PERSON> 3, <PERSON><PERSON> <PERSON>, tỉnh <PERSON><PERSON>ng <PERSON>", 
      email: commonInfo?.email ?? "<EMAIL>",
      google_map_link: commonInfo?.google_map_link ?? "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3924.8109477379166!2d106.67020109678955!3d10.356996300000015!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31754f002909cebd%3A0x48e8296554c936ed!2sNha%20Khoa%20246%20-USmile%20Dental%20Office!5e0!3m2!1svi!2s!4v1752676492429!5m2!1svi!2s",
      name: commonInfo?.name ?? "Nha Khoa 246",
      name2: commonInfo?.name2 ?? "USmile Dental",
      logo_link: commonInfo?.logo_link ?? "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/logo_xorkjq.jpg",
      service_list: commonInfo?.service_list ?? [
        'Nhổ Răng Khôn',
        'Chỉnh Nha',
        'Trồng Răng Trên Implant',
        'Nha Khoa Thẩm Mỹ'
      ],
    });

    useEffect(() => {
          if (!commonInfo) return;
  
          setOtherInfoData(commonInfo);
      }, [commonInfo]);
  
      const handleSave = () => {
          if (otherInfoData.id !== 0) {
              router.put(
                  route('common-infos.update', otherInfoData.id), 
                  otherInfoData, 
                  {
                      preserveScroll: true,
                      onSuccess: () => toast.success("Cập nhật thành công!")
                  });
              
          } else {
              router.post(route('common-infos.store'), 
                  otherInfoData,
                  {
                      preserveScroll: true,
                      onSuccess: () => toast.success("Cập nhật thành công!")
                  });
          }
      };
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            Thông tin khác
          </CardTitle>
          <CardDescription>Quản lý thông tin khác</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h4 className="font-medium mb-4">Danh sách dịch vụ</h4>
            <div className="space-y-3">
                {otherInfoData.service_list.map((service, index) => (
                    <div key={index} className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground w-8">#{index + 1}</span>
                        <Input
                            value={service}
                            onChange={(e) => {
                            const updated = [...otherInfoData.service_list];
                            updated[index] = e.target.value;
                            setOtherInfoData({...otherInfoData, service_list: updated});
                            }}
                            placeholder="Service Name"
                        />
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                            const updated = otherInfoData.service_list.filter((_, i) => i !== index);
                            setOtherInfoData({...otherInfoData, service_list: updated});
                            }}
                        >
                            <Trash2 className="w-4 h-4" />
                        </Button>
                    </div>
                ))}
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setOtherInfoData({
                    ...otherInfoData, 
                    service_list: [...otherInfoData.service_list, ""]
                    })}
                >
                    <Plus className="w-4 h-4" />
                    Thêm dịch vụ
                </Button>
            </div>
        </div>

          <div className="flex justify-end">
            <Button onClick={() => handleSave()}>
              <Save className="w-4 h-4" />
              Lưu thay đổi
            </Button>
          </div>
        </CardContent>
      </Card>
    )
}

export default OtherInfo;