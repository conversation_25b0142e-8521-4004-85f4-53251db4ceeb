import { PageProps } from '@/types';
import { Head } from '@inertiajs/react';
import DeleteUserForm from './PartialsVN/DeleteUserForm';
import UpdatePasswordForm from './PartialsVN/UpdatePasswordForm';
import UpdateProfileInformationForm from './PartialsVN/UpdateProfileInformationForm';
import AppLayout from '@/Layouts/AppLayout';

const breadcrumbs = [
    {
      title: "Tài Khoản",
      href: "/profile",
    },
];

export default function Edit({
    mustVerifyEmail,
    status,
}: PageProps<{ mustVerifyEmail: boolean; status?: string }>) {
    return (
         <AppLayout breadcrumbs={breadcrumbs}>
              <Head title="Profile" />
              {/* <div className="flex h-full flex-1 flex-col space-y-8 p-4"> */}

            <div className="py-8">
                <div className="mx-auto max-w-7xl space-y-6 sm:px-6 lg:px-8">
                    <div className="bg-white p-4 shadow sm:rounded-lg sm:p-8">
                        <UpdateProfileInformationForm
                            mustVerifyEmail={mustVerifyEmail}
                            status={status}
                            className="max-w-xl"
                        />
                    </div>

                    <div className="bg-white p-4 shadow sm:rounded-lg sm:p-8">
                        <UpdatePasswordForm className="max-w-xl" />
                    </div>

                    {/* <div className="bg-white p-4 shadow sm:rounded-lg sm:p-8">
                        <DeleteUserForm className="max-w-xl" />
                    </div> */}
                </div>
            </div>
        </AppLayout>
    );
}
