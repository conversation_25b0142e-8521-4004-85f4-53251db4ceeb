<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('about_heroes', function (Blueprint $table) {
            $table->id();
            $table->string('main_title')->nullable();
            $table->text('subtitle')->nullable();
            $table->string('primary_button_text')->nullable();
            $table->string('background_video')->nullable();
            $table->json('stats')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('about_heroes');
    }
};
