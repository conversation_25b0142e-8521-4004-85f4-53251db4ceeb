import { usePage } from "@inertiajs/react";
import { ResourceHero } from "@/types";

const ResourcesHero = () => {
  const cloudName = usePage().props.cloudName;
  const publicId = "Resource_Hero_sttpgo";

  const resourceHero = usePage().props.resourceHero as ResourceHero;

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-emerald-50 to-white">
      {/* Background Video */}
      <div className="absolute inset-0">
        <video
          autoPlay
          muted
          loop
          playsInline
          className="w-full h-full object-cover"
        >
          <source src={resourceHero?.background_video ?? `https://res.cloudinary.com/${cloudName}/video/upload/q_auto,f_auto/${publicId}.mp4`} type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* Video overlay */}
        <div className="absolute inset-0 bg-white/30" />
      </div>

      {/* Hero Content */}
      <div className="relative z-10 container mx-auto px-4 pt-24">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          <div className="space-y-8 animate-fade-in">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
              {resourceHero?.main_title ?? "Gặp Gỡ"}
              <span className="text-emerald-600 block">{resourceHero?.title_highlight ?? "Đội Ngũ Chuyên Gia Của Chúng Tôi"}</span>
            </h1>
            <p className="text-lg text-gray-800 leading-relaxed max-w-lg">
              {resourceHero?.subtitle ?? "Khám phá những chuyên gia tận tâm đứng sau chất lượng chăm sóc vượt trội tại Nha Khoa 246. Đội ngũ giàu kinh nghiệm của chúng tôi kết hợp giữa chuyên môn tiên tiến và sự tận tâm chân thành để mang đến trải nghiệm nha khoa tuyệt vời cho bạn."}
            </p>
            {/* <div className="flex flex-col sm:flex-row gap-4">
              <Button>
                Meet Our Team
              </Button>
              <Button variant="landing_outline">
                Book Consultation
              </Button>
            </div> */}
          </div>
          
          {/* <div className="relative">
            <div className="relative z-10 animate-scale-in">
              <img 
                src="https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/Hero_ysopbl.jpg" 
                alt="Professional dental team" 
                className="w-full h-full object-cover rounded-2xl shadow-2xl"
              />
            </div>
            <div className="absolute -top-6 -right-6 w-72 h-72 bg-emerald-100 rounded-full -z-10"></div>
            <div className="absolute -bottom-6 -left-6 w-48 h-48 bg-green-100 rounded-full -z-10"></div>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default ResourcesHero;