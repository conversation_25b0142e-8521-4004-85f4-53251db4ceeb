import { But<PERSON> } from "@/Components/ui/button";
import { Link, usePage } from "@inertiajs/react";
import { Phone, MapPin, Mail } from "lucide-react";
import { useState, useEffect } from "react";
import { HomeHero, CommonInfo } from "@/types";

const HeroSlideImage = () => {
  const address = usePage().props.address as string;
  const homeHero = usePage().props.homeHero as HomeHero;
  const commonInfo = usePage().props.commonInfo as CommonInfo;
  
  const [currentSlide, setCurrentSlide] = useState(0);
  const img1 = "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/home-slide-1_q0pcp9.jpg";
  const img2 = "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/home-slide-2_za9ybq.jpg";
  const img4 = "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/home-slide-4_qfyykj.jpg";
  const img5 = "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/home-slide-5_rzgzhu.jpg";
  const img7 = "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/home-slide-7_wicigk.jpg";
  const img8 = "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/home-slide-8_scmfif.jpg";
  const img9 = "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/home-slide-9_v2hzuq.jpg";
  
  const background_images = homeHero?.background_images ?? [img1, img2, img4, img5, img7, img8, img9];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % background_images.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [background_images.length]);

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Background Image Slider */}
      <div className="absolute inset-0">
        {background_images.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 bg-cover bg-center bg-no-repeat transition-opacity duration-1000 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
            style={{ backgroundImage: `url(${image})` }}
          />
        ))}
        {/* Dark overlay */}
        {/* <div className="absolute inset-0 bg-black/30" /> */}
        {/* Light overlay */}
        <div className="absolute inset-0 bg-white/30" />
      </div>

      {/* Hero Content */}
      <div className="relative z-10 container mx-auto px-4 pt-24">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          <div className="space-y-6 md:space-y-8 animate-fade-in max-w-lg lg:max-w-full">
            {/* <h1 className="text-5xl lg:text-5xl font-bold text-white leading-tight"> */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 leading-tight">
              {homeHero?.main_title ?? "Nụ Cười Của Bạn,"}
              <span className="text-emerald-600 block">{homeHero?.title_highlight ?? "Ưu Tiên Của Chúng Tôi"}</span>
            </h1>
            {/* <p className="text-xl lg:text-2xl text-white leading-relaxed"> */}
            <p className="text-lg text-gray-900 leading-relaxed max-w-sm md:max-w-lg">
              {homeHero?.subtitle ?? "Trung tâm nha khoa hiện đại, đội ngũ bác sĩ tận tâm – nơi bạn luôn cảm thấy an toàn, thõa mái và được chăm sóc tận tình."}
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/about">
                <Button>
                  {homeHero?.primary_button_text ?? "Tìm Hiểu Ngay"}
                </Button>
              </Link>
            </div>
            
            {/* Quick Info */}
            <div className="grid grid-cols-1 md:grid-cols-1 gap-3 md:gap-6 pt-4 md:pt-8 pb-4">
              {/* <div className="flex items-center space-x-3 text-white"> */}
              <div className="flex items-center space-x-3 text-gray-800 text-lg">
                <Phone className="w-5 h-5 text-emerald-600" />
                <span>{commonInfo?.phone ?? "0273.6588.988"} - {commonInfo?.phone2 ?? "0939.809.246"}</span>
              </div>
              {/* <div className="flex items-center space-x-3 text-white"> */}
              <div className="flex items-center space-x-3 text-gray-800 text-lg">
                <div className="w-5 h-5"><MapPin className="w-5 h-5 text-emerald-600" /></div>
                <span>{commonInfo?.location ?? address}</span>
              </div>
              {/* <div className="flex items-center space-x-3 text-white"> */}
              <div className="flex items-center space-x-3 text-gray-800 text-lg">
                <Mail className="w-5 h-5 text-emerald-600" />
                <span>{commonInfo?.email ?? "<EMAIL>"}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Slide Indicators */}
      {/* <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
        {background_images.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide 
                ? 'bg-white' 
                : 'bg-white/40 hover:bg-white/60'
            }`}
          />
        ))}
      </div> */}
    </div>
  );
};

export default HeroSlideImage;