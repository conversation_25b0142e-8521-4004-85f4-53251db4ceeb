import { Link, usePage } from "@inertiajs/react";
import { Phone, Mail, MapPin } from "lucide-react";
import { CommonInfo } from "@/types";

const Footer = () => {
  const address = usePage().props.address as string;
  const commonInfo = usePage().props.commonInfo as CommonInfo;
  const serviceList = commonInfo?.service_list ?? [
    'Nhổ Răng Khôn',
    'Chỉnh Nha',
    'Trồng Răng Trên Implant',
    'Nha K<PERSON>a Thẩm Mỹ'
  ];
  const logoLink = commonInfo?.logo_link ?? "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/logo_xorkjq.jpg";

  return (
    <footer className="bg-gray-900 text-white pt-16 pb-8">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4 w-full md:w-2/3">
            <div className="flex items-center space-x-2">
              <div className="w-10 h-10 rounded-full flex items-center justify-center">
                  <img src={logoLink} alt="logo" className="rounded-full" />
              </div>
              <span className="text-xl font-panteka font-bold">{commonInfo?.name ?? "Nha Khoa 246"}</span>
            </div>
            <p className="text-gray-400 leading-relaxed">
              Cung cấp dịch vụ chăm sóc răng miệng đặc biệt với công nghệ tiên tiến và dịch vụ tận tâm trong hơn 5 năm.
            </p>
            {/* <div className="flex space-x-4">
              <Facebook className="w-5 h-5 text-gray-400 hover:text-emerald-400 cursor-pointer transition-colors" />
              <Instagram className="w-5 h-5 text-gray-400 hover:text-pink-400 cursor-pointer transition-colors" />
              <Twitter className="w-5 h-5 text-gray-400 hover:text-emerald-300 cursor-pointer transition-colors" />
              <Youtube className="w-5 h-5 text-gray-400 hover:text-red-400 cursor-pointer transition-colors" />
            </div> */}
          </div>
          
          {/* Services */}
          <div>
            <h3 className="text-lg font-panteka font-semibold mb-4">Dịch Vụ</h3>
            <ul className="space-y-2 text-gray-400">
              {serviceList.map((service, index) => (
                <li key={index} className="hover:text-white cursor-pointer transition-colors">{service}</li>
              ))}
            </ul>
          </div>
          
          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-panteka font-semibold mb-4">Liên Kết Nhanh</h3>
            <ul className="space-y-2 text-gray-400">
              <li className="hover:text-white cursor-pointer transition-colors"><Link href="/about">Về Chúng Tôi</Link></li>
              <li className="hover:text-white cursor-pointer transition-colors"><Link href="/resource">Đội Ngũ Nha Khoa</Link></li>
              <li className="hover:text-white cursor-pointer transition-colors"><Link href="/contact">Liên Hệ</Link></li>
            </ul>
          </div>
          
          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-panteka font-semibold mb-4">Thông Tin Liên Lạc</h3>
            <div className="space-y-3 text-gray-400">
              <div className="flex items-center space-x-3">
                <Phone className="w-4 h-4" />
                <span>{commonInfo?.phone ?? "0273.6588.988"} - {commonInfo?.phone2 ?? "0939.809.246"}</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="w-4 h-4" />
                <span className="break-all">{commonInfo?.email ?? "<EMAIL>"}</span>
              </div>
              <div className="flex items-start space-x-3">
                <div className="size-4"><MapPin className="size-4 mt-1" /></div>
                
                <span>{commonInfo?.location ?? address}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-start items-center">
            <p className="text-gray-400 text-sm">
              © 2025 Nha Khoa 246. Bảo lưu mọi quyền.
            </p>
            {/* <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors">
                Điều Khoản Dịch Vụ
              </a>
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors">
                Chính Sách Riêng Tư
              </a>
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors">
                Chính Sách Cookie
              </a>
            </div> */}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;