import { Button } from "@/Components/ui/button";
import { Award, Users, Clock, MapPin } from "lucide-react";

const About = () => {
  const stats = [
    { icon: <Users className="w-6 h-6" />, number: "500+", label: "<PERSON>ạnh <PERSON>ng" },
    { icon: <Award className="w-6 h-6" />, number: "15+", label: "Năm Kinh Nghiệm" },
    { icon: <Clock className="w-6 h-6" />, number: "24/7", label: "Nha Khoa Ngoại Trú" },
    { icon: <MapPin className="w-6 h-6" />, number: "3", label: "Trung Tâm" }
  ];

  return (
    <section id="about" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-8">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                V<PERSON> Chúng Tôi
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                Trải nghiệm dịch vụ nha khoa tốt nhất với trung tâm nha khoa hiện đại và đội ngũ bác sĩ tận tâm. 
                Chúng tôi cam kết mang lại trải nghiệm nha khoa thoải mái và thoải mái cho bạn.
              </p>
              <p className="text-lg text-gray-600 leading-relaxed">
                Chúng tôi cam kết mang lại trải nghiệm nha khoa thoải mái và thoải mái cho bạn. 
                Chúng tôi luôn mong muốn mang đến cho bạn những trải nghiệm tốt nhất.
              </p>
            </div>
            
            <div className="grid grid-cols-2 gap-6">
              {stats.map((stat, index) => (
                <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="flex justify-center mb-2 text-emerald-600">
                    {stat.icon}
                  </div>
                  <div className="text-2xl font-bold text-gray-900">{stat.number}</div>
                  <div className="text-sm text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
            
            <Button size="lg" className="bg-emerald-600 hover:bg-emerald-700">
              Tìm hiểu thêm về chúng tôi
            </Button>
          </div>
          
          <div className="relative">
            <div className="grid grid-cols-2 gap-4">
              <img 
                src="https://images.unsplash.com/photo-1551601651-2a8555f1a136?auto=format&fit=crop&q=80" 
                alt="Đội Ngũ Bác Sĩ" 
                className="w-full h-48 object-cover rounded-lg shadow-lg"
              />
              <img 
                src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?auto=format&fit=crop&q=80" 
                alt="Thiết Bị Nha Khoa" 
                className="w-full h-48 object-cover rounded-lg shadow-lg mt-8"
              />
              <img 
                src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?auto=format&fit=crop&q=80" 
                alt="Tư Vấn Nha Khoa" 
                className="w-full h-48 object-cover rounded-lg shadow-lg -mt-8"
              />
              <img 
                src="https://images.unsplash.com/photo-1559757175-0eb30cd8c063?auto=format&fit=crop&q=80" 
                alt="Nội Thất Nha Khoa" 
                className="w-full h-48 object-cover rounded-lg shadow-lg"
              />
            </div>
            <div className="absolute -inset-4 bg-gradient-to-r from-emerald-100 to-green-100 rounded-2xl -z-10 opacity-30"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;