import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title, CardDescription } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import { Textarea } from "@/Components/ui/textarea";
import { Badge } from "@/Components/ui/badge";
import { Users, Plus, Save, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Separator } from "@/Components/ui/separator";
import { router, usePage } from "@inertiajs/react";
import { Title, Doctor as DoctorType } from "@/types";

const Doctor = () => {
    const doctorTitle = usePage().props.doctorTitle as Title;
    const doctors = usePage().props.doctors as DoctorType[];

    // Real Doctor State
    const [doctorsData, setDoctorsData] = useState({
        id: doctorTitle?.id ?? 0,
        main_title: doctorTitle?.main_title ?? "Your Smile,",
        subtitle: doctorTitle?.subtitle ?? "Experience exceptional dental care with compassionate professionals dedicated to your comfort and oral health.",
        section_name: "doctors",
        doctors: doctors ?? []
    });

    // Doctor Functions
    const addStory = () => {
        setDoctorsData({...doctorsData, doctors: [...doctorsData.doctors, {
            id: "Id" + Date.now(),
            name: "Tên bác sĩ",
            description: "",
            image: "",
        }]});
    };

    const removeStory = (index: number) => {
        setDoctorsData({...doctorsData, doctors: doctorsData.doctors.filter((_, i) => i !== index)});
    };

    const updateStory = (index: number, field: string, value: any) => {
        const updated = [...doctorsData.doctors];
        updated[index] = { ...updated[index], [field]: value };
        setDoctorsData({...doctorsData, doctors: updated});
    };

    useEffect(() => {
        if (!doctorTitle) return;

        setDoctorsData({
            doctors: doctors ?? [],
            ...doctorTitle
        });
    }, [doctorTitle, doctors]);

    const handleSave = () => {
        if (doctorsData.id !== 0) {
            router.put(
                route('doctors.update', doctorsData.id), 
                doctorsData as any, 
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
            
        } else {
            router.post(route('doctors.store'), 
                doctorsData as any,
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
        }
    };

    return (
        <Card>
            <CardHeader>
            <CardTitle className="flex items-center justify-between">
                <span className="flex items-center">
                    <Users className="w-5 h-5 mr-2" />
                    Danh sách bác sĩ
                </span>
                {/* <Button onClick={addStory} size="sm">
                    <Plus className="w-4 h-4" />
                    Thêm chia sẻ
                </Button> */}
            </CardTitle>
            <CardDescription>Quản lý nội dung và hình ảnh bác sĩ</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="space-y-4">
                    <div>
                        <Label htmlFor="main_title">Tiêu đề</Label>
                        <Input
                        id="main_title"
                        value={doctorsData.main_title}
                        onChange={(e) => setDoctorsData({...doctorsData, main_title: e.target.value})}
                        />
                    </div>
                    <div>
                        <Label htmlFor="subtitle">Phụ đề</Label>
                        <Textarea
                        id="subtitle"
                        value={doctorsData.subtitle}
                        onChange={(e) => setDoctorsData({...doctorsData, subtitle: e.target.value})}
                        rows={3}
                        />
                    </div>
                </div>

                <Separator />
                
                <div className="space-y-4">
                    <h4 className="font-medium">Danh sách bác sĩ</h4>
                    {doctorsData.doctors.map((story, index) => (
                        <Card key={story.id} className="relative">
                        <CardHeader className="pb-4">
                            <div className="flex items-center justify-between">
                            <Badge>Bác sĩ {index + 1}</Badge>
                            <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => removeStory(index)}
                            >
                                <Trash2 className="w-4 h-4" />
                            </Button>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label>Tên bác sĩ</Label>
                                <Input
                                    value={story.name}
                                    onChange={(e) => updateStory(index, 'name', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label>Nội dung</Label>
                                <Textarea
                                    value={story.description}
                                    onChange={(e) => updateStory(index, 'description', e.target.value)}
                                    rows={3}
                                />
                            </div>
                            <div>
                                <Label>Link ảnh</Label>
                                <Input
                                    value={story.image}
                                    onChange={(e) => updateStory(index, 'image', e.target.value)}
                                    placeholder="Link ảnh"
                                />
                            </div>
                        </CardContent>
                        </Card>
                    ))}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={addStory}
                    >
                        <Plus className="w-4 h-4" />
                        Thêm bác sĩ
                    </Button>
                </div>

                <div className="flex justify-end">
                    <Button onClick={() => handleSave()}>
                    <Save className="w-4 h-4" />
                        Lưu thay đổi
                    </Button>
                </div>
                </CardContent>
        </Card>
    );
}

export default Doctor;