
import { usePage } from "@inertiajs/react";
import { Title } from "@/types";

const OurMissionMultipleImage = () => {
  const ourMissionTitle = usePage().props.ourMissionTitle as Title;
  const peopleData = usePage().props.ourMissions as string[];
  // const peopleData = [
  //   {
  //     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/BS_Phước_Zoomed_vu54iy.png",
  //     alt: "Dental team member 1"
  //   },
  //   {
  //     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/BS_Thanh_czovsr.png",
  //     alt: "Dental team member 2"
  //   },
  //   {
  //     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/BS_Hu%C3%A2n_y6dbpi.png",
  //     alt: "Dental team member 3"
  //   },
  //   {
  //     image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/BS_Thùy_Zoomed_vdafi9.png",
  //     alt: "Dental team member 4"
  //   }
  // ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Images Grid */}
          <div className="grid grid-cols-2 gap-6 pr-6">
            <div className="space-y-6">
              <img 
                src={peopleData[0] ?? "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/BS_Phước_Zoomed_vu54iy.png"}
                alt={peopleData[0] ?? "Dental doctor 1"}
                className="w-full h-60 sm:h-64 object-cover sm:object-contain rounded-2xl shadow-lg bg-background"
              />
              <img 
                src={peopleData[1] ?? "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/BS_Thanh_czovsr.png"}
                alt={peopleData[1] ?? "Dental doctor 2"}
                className="w-full h-60 object-cover sm:object-contain rounded-2xl shadow-lg bg-background"
              />
            </div>
            <div className="sm:pt-12 space-y-6">
              <img 
                src={peopleData[2] ?? "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/BS_Hu%C3%A2n_y6dbpi.png"}
                alt={peopleData[2] ?? "Dental doctor 3"}
                className="w-full h-60 sm:h-80 object-cover rounded-2xl shadow-lg bg-background"
              />
              <img 
                src={peopleData[3] ?? "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/BS_Thùy_Zoomed_vdafi9.png"}
                alt={peopleData[3] ?? "Dental doctor 4"}
                className="w-full sm:w-[80%] h-60 sm:h-56 object-cover sm:object-contain rounded-2xl shadow-lg bg-background"
              />
            </div>
          </div>
          
          {/* Text Content */}
          <div className="space-y-8">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6 leading-tight">
                {ourMissionTitle?.main_title ?? "Sứ mệnh của chúng tôi"}
              </h2>
              <div className="w-24 h-1 bg-emerald-600 mb-8"></div>
            </div>
            
            <div className="space-y-6">
              <p className="text-lg text-gray-600 leading-relaxed max-w-xl">
                {ourMissionTitle?.subtitle ?? "Chúng tôi tin rằng sự tiến bộ của y học không chỉ nằm ở kỹ thuật điều trị, mà còn ở khả năng tiếp cận. Bằng việc ứng dụng những công nghệ hiện đại nhất, chúng tôi không ngừng nâng cao hiệu quả lâm sàng – đồng thời tối ưu chi phí để nhiều bệnh nhân có cơ hội được chăm sóc y tế chất lượng, nhân văn và bền vững."}
              </p>
            </div>
            
            {/* <Button>
              Join Our Team
            </Button> */}
          </div>
        </div>
      </div>
    </section>
  );
};

export default OurMissionMultipleImage;