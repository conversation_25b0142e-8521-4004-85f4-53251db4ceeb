import { Card, CardContent } from "@/Components/ui/card";

const TeamGrid = () => {
  const teamMembers = [
    {
      name: "Dr. <PERSON>",
      qualifications: "DDS, MSD Orthodontics",
      image: "https://images.unsplash.com/photo-**********-2b71ea197ec2?auto=format&fit=crop&q=80",
      details: "Specialist in orthodontics with over 12 years of experience in creating beautiful smiles through advanced alignment techniques."
    },
    {
      name: "Dr. <PERSON>",
      qualifications: "DDS, Oral Surgery",
      image: "https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?auto=format&fit=crop&q=80",
      details: "Expert oral surgeon specializing in dental implants and complex extractions with a gentle, patient-centered approach."
    },
    {
      name: "Dr. <PERSON>",
      qualifications: "DDS, Cosmetic Dentistry",
      image: "https://images.unsplash.com/photo-1582750433449-648ed127bb54?auto=format&fit=crop&q=80",
      details: "Passionate about cosmetic dentistry, helping patients achieve their dream smiles through veneers, whitening, and smile makeovers."
    },
    {
      name: "<PERSON>",
      qualifications: "RDH, Periodontal Therapy",
      image: "https://images.unsplash.com/photo-1594824756810-8cdddadece92?auto=format&fit=crop&q=80",
      details: "Registered dental hygienist specializing in periodontal therapy and preventive care with exceptional patient communication skills."
    },
    {
      name: "James Kim",
      qualifications: "CDA, Digital Imaging",
      image: "https://images.unsplash.com/photo-1622253692010-333f2da6031d?auto=format&fit=crop&q=80",
      details: "Certified dental assistant with expertise in digital imaging and advanced dental technology, ensuring precise treatment planning."
    },
    {
      name: "Maria Garcia",
      qualifications: "Office Manager, Patient Care",
      image: "https://images.unsplash.com/photo-1594824756810-8cdddadece92?auto=format&fit=crop&q=80",
      details: "Dedicated office manager focused on creating positive patient experiences and coordinating comprehensive care plans."
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Meet the Alba TeamGrid
          </h2>
          {/* <div className="w-24 h-1 bg-emerald-600 mx-auto mb-8"></div> */}
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Get to know the exceptional professionals who make Alba Dental a place 
            where expertise meets compassion, and where every patient receives 
            personalized, world-class care.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {teamMembers.map((member, index) => (
            <Card key={index} className="bg-white hover:shadow-lg transition-shadow duration-300 overflow-hidden">
              <CardContent className="p-0">
                <div className="relative overflow-hidden">
                  <img 
                    src={member.image}
                    alt={member.name}
                    className="w-full h-64 object-cover transition-transform duration-300 hover:scale-105"
                  />
                </div>
                <div className="p-6 space-y-4">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-1">
                      {member.name}
                    </h3>
                    <p className="text-emerald-600 font-semibold text-sm mb-3">
                      {member.qualifications}
                    </p>
                  </div>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {member.details}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TeamGrid;