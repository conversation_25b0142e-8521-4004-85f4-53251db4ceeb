import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/Components/ui/tabs";
import { But<PERSON> } from "@/Components/ui/button";
import { Badge } from "@/Components/ui/badge";
import { 
  Setting<PERSON>, 
  <PERSON>, 
} from "lucide-react";
import { toast } from "sonner";
import AppLayout from "@/Layouts/AppLayout";
import { Head } from "@inertiajs/react";
import Hero from "@/Components/AdminSections/Home/Hero";
import Activities from "@/Components/AdminSections/Home/Activities";
import CustomerReview from "@/Components/AdminSections/Home/CustomerReview";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/Components/ui/dialog";
import HomePage from "../Site/Home";

const Admin = () => {
  // Services State
  const [services, setServices] = useState([
    {
      title: "General Dentistry",
      description: "Comprehensive dental care including cleanings, fillings, and preventive treatments.",
      features: ["Regular Checkups", "Teeth Cleaning", "Cavity Treatment"],
      icon: "Smile",
      color: "blue"
    },
    {
      title: "Cosmetic Dentistry", 
      description: "Transform your smile with veneers, whitening, and aesthetic treatments.",
      features: ["Teeth Whitening", "Dental Veneers", "Smile Makeover"],
      icon: "Star",
      color: "green"
    },
    {
      title: "Oral Surgery",
      description: "Expert surgical procedures including extractions and implant placement.",
      features: ["Tooth Extraction", "Dental Implants", "Wisdom Teeth"],
      icon: "Shield", 
      color: "purple"
    }
  ]);

  // About Section State
  const [aboutData, setAboutData] = useState({
    title: "About Alba Dental Clinic",
    description1: "For over 15 years, Alba Dental has been committed to providing exceptional dental care to families across Malaysia. Our state-of-the-art facility combines cutting-edge technology with a warm, welcoming environment.",
    description2: "Our team of experienced dentists and hygienists are dedicated to ensuring your comfort while delivering the highest quality dental treatments. We believe in preventive care and patient education to help you maintain optimal oral health.",
    stats: [
      { number: "500+", label: "Happy Patients", icon: "Users" },
      { number: "15+", label: "Years Experience", icon: "Award" },
      { number: "24/7", label: "Emergency Care", icon: "Clock" },
      { number: "3", label: "Locations", icon: "MapPin" }
    ],
    images: [
      "https://images.unsplash.com/photo-**********-2a8555f1a136?auto=format&fit=crop&q=80",
      "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?auto=format&fit=crop&q=80",
      "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?auto=format&fit=crop&q=80",
      "https://images.unsplash.com/photo-**********-0eb30cd8c063?auto=format&fit=crop&q=80"
    ]
  });

  const handleSave = (section: string) => {
    toast.success("Cập nhật thành công!");
  };

  const addService = () => {
    setServices([...services, {
      title: "New Service",
      description: "Service description",
      features: ["Feature 1", "Feature 2"],
      icon: "Smile",
      color: "blue"
    }]);
  };

  const removeService = (index: number) => {
    setServices(services.filter((_, i) => i !== index));
  };

  const updateService = (index: number, field: string, value: any) => {
    const updated = [...services];
    updated[index] = { ...updated[index], [field]: value };
    setServices(updated);
  };

  const breadcrumbs = [
    {
      title: "Trang Chủ",
      href: "/admin",
    },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Trang Chủ" />
      <div className="flex h-full flex-1 flex-col space-y-8 p-4">

        <div className="min-h-screen bg-background">
          {/* Admin Header */}
          <div className="bg-card">
            <div className="container mx-auto">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                    <Settings className="w-5 h-5 text-primary-foreground" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold">Nha Khoa 246 Admin</h1>
                    <p className="text-muted-foreground">Quản lý nội dung trang chủ</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {/* <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4" />
                        Preview Site
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-auto p-0">
                      <HomePage />
                    </DialogContent>
                  </Dialog> */}
                  <Button variant="outline" size="sm" onClick={() => window.open('/', '_blank')}>
                    <Eye className="w-4 h-4" />
                    Xem Trang Chủ
                  </Button>
                  {/* <Badge variant="secondary">Live</Badge> */}
                </div>
              </div>
            </div>
          </div>

          <div className="container mx-auto px-4 py-8">
            <Tabs defaultValue="hero" className="space-y-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="hero">Hero</TabsTrigger>
                <TabsTrigger value="activities">Hoạt động nha khoa</TabsTrigger>
                <TabsTrigger value="customerReview">Chia sẻ của khách hàng</TabsTrigger>
              </TabsList>

              <TabsContent value="hero" className="space-y-6">
                <Hero />
              </TabsContent>

              <TabsContent value="activities" className="space-y-6">
                <Activities />
              </TabsContent>

              <TabsContent value="customerReview" className="space-y-6">
                <CustomerReview />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default Admin;