<?php

use App\Http\Controllers\AboutClientController;
use App\Http\Controllers\AboutController;
use App\Http\Controllers\AboutHeroController;
use App\Http\Controllers\ActivityController;
use App\Http\Controllers\CommonInfoController;
use App\Http\Controllers\ContactClientController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\CustomerReviewController;
use App\Http\Controllers\DoctorController;
use App\Http\Controllers\EquipmentController;
use App\Http\Controllers\FourCoreValueController;
use App\Http\Controllers\HomeClientController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\HomeHeroController;
use App\Http\Controllers\OurMissionController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ResourceClientController;
use App\Http\Controllers\ResourceController;
use App\Http\Controllers\ResourceHeroController;
use App\Http\Controllers\TeamController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', [HomeClientController::class, 'index']);
Route::get('/about', [AboutClientController::class, 'index']);
Route::get('/resource', [ResourceClientController::class, 'index']);
Route::get('/contact', [ContactClientController::class, 'index']);

// Route::get('/dashboard', function () {
//     return Inertia::render('Dashboard');
// })->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    // Home Page Routes
    Route::get('/admin', [HomeController::class, 'index'])->name('trang-chu');

    Route::post('/home-hero', [HomeHeroController::class,'store'])->name('home-hero.store');
    Route::put('/home-hero/{homeHero}', [HomeHeroController::class,'update'])->name('home-hero.update');

    Route::post('/activities', [ActivityController::class,'store'])->name('activities.store');
    Route::put('/activities/{sectionTitle}', [ActivityController::class,'update'])->name('activities.update');

    Route::post('/customer-reviews', [CustomerReviewController::class,'store'])->name('customer-reviews.store');
    Route::put('/customer-reviews/{sectionTitle}', [CustomerReviewController::class,'update'])->name('customer-reviews.update');

    // About Page Routes
    Route::get('/admin/gioi-thieu', [AboutController::class, 'index'])->name('gioi-thieu');

    Route::post('/about-hero', [AboutHeroController::class,'store'])->name('about-hero.store');
    Route::put('/about-hero/{aboutHero}', [AboutHeroController::class,'update'])->name('about-hero.update');

    Route::post('/four-core-values', [FourCoreValueController::class,'store'])->name('four-core-values.store');
    Route::put('/four-core-values/{sectionTitle}', [FourCoreValueController::class,'update'])->name('four-core-values.update');

    Route::post('/our-missions', [OurMissionController::class,'store'])->name('our-missions.store');
    Route::put('/our-missions/{sectionTitle}', [OurMissionController::class,'update'])->name('our-missions.update');

    Route::post('/equipments', [EquipmentController::class,'store'])->name('equipments.store');
    Route::put('/equipments/{sectionTitle}', [EquipmentController::class,'update'])->name('equipments.update');

    // Resource Page Routes
    Route::get('/admin/doi-ngu-nha-khoa', [ResourceController::class, 'index'])->name('doi-ngu-nha-khoa');

    Route::post('/resource-hero', [ResourceHeroController::class,'store'])->name('resource-hero.store');
    Route::put('/resource-hero/{resourceHero}', [ResourceHeroController::class,'update'])->name('resource-hero.update');

    Route::post('/doctors', [DoctorController::class,'store'])->name('doctors.store');
    Route::put('/doctors/{sectionTitle}', [DoctorController::class,'update'])->name('doctors.update');

    Route::post('/teams', [TeamController::class,'store'])->name('teams.store');
    Route::put('/teams/{sectionTitle}', [TeamController::class,'update'])->name('teams.update');

    Route::post('/contacts', [ContactController::class,'store'])->name('contacts.store');
    Route::put('/contacts/{sectionTitle}', [ContactController::class,'update'])->name('contacts.update');

    // Common Info Routes
    Route::get('/admin/thong-tin-chung', [CommonInfoController::class, 'index'])->name('thong-tin-chung');

    Route::post('/common-infos', [CommonInfoController::class,'store'])->name('common-infos.store');
    Route::put('/common-infos/{commonInfo}', [CommonInfoController::class,'update'])->name('common-infos.update');

    // Profile Routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
