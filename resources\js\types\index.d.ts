export interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at?: string;
}

export type PageProps<
    T extends Record<string, unknown> = Record<string, unknown>,
> = T & {
    auth: {
        user: User;
    };
};

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface CommonInfo {
    id: number;
    name: string;
    name2: string;
    logo_link: string;
    phone: string;
    phone2: string;
    location: string;
    email: string;
    google_map_link: string;
    service_list: string[];
}

export interface Title {
    id: number;
    section_name: string;
    main_title: string;
    subtitle: string;
}

export interface HomeHero {
    id: number;
    main_title: string;
    title_highlight: string;
    subtitle: string;
    primary_button_text: string;
    background_images: string[];
    background_images_mobile: string[];
    contact_info: Partial<CommonInfo>;
}

export type Activity = {
    id: string;
    shorttitle: string;
    longtitle: string;
    short_description: string;
    long_description: string;
    image: string;
    type: "image" | "video";
    date: string;
    participants: string;
    media_url: string;
    thumbnailUrl?: string;
};

export type CustomerReview = {
    id: string;
    type: "image" | "video";
    image: string;
    media_url: string;
};

export interface AboutHero {
    id: number;
    main_title: string;
    subtitle: string;
    primary_button_text: string;
    background_video: string;
    stats: { number: string; label: string }[];
}

export interface Equipment {
    id: string;
    title: string;
    brief: string;
    description: string;
    type: string;
    image: string;
    media_url: string;
}

export interface Doctor {
    id: string;
    name: string;
    description: string;
    image: string;
}

export interface Team {
    position: string;
    image: string;
}

export interface ResourceHero {
    id: number;
    main_title: string;
    title_highlight: string;
    subtitle: string;
    background_video: string;
}