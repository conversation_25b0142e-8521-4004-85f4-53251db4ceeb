<?php

namespace App\Http\Controllers;

use App\Models\AboutHero;
use App\Models\CommonInfo;
use App\Models\Equipment;
use App\Models\FourCoreValue;
use App\Models\OurMission;
use App\Models\SectionTitle;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Application;

class AboutClientController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Site/About', [
            'aboutHero' => AboutHero::first(),
            'fourCoreValueTitle' => SectionTitle::where('section_name', 'four_core_values')->first(),
            'fourCoreValues' => FourCoreValue::first() ? FourCoreValue::first()->core_values : [],
            'ourMissionTitle' => SectionTitle::where('section_name', 'our_missions')->first(),
            'ourMissions' => OurMission::first() ? OurMission::first()->doctors : [],
            'equipmentTitle' => SectionTitle::where('section_name', 'equipments')->first(),
            'equipments' => Equipment::all(),
            'commonInfo' => CommonInfo::first(),

            'canLogin' => Route::has('login'),
            'canRegister' => Route::has('register'),
            'laravelVersion' => Application::VERSION,
            'phpVersion' => PHP_VERSION,
        ]);
    }
}
