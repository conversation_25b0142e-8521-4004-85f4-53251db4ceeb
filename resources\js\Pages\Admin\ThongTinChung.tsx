import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/Components/ui/tabs";
import { Settings } from "lucide-react";
import AppLayout from "@/Layouts/AppLayout";
import { Head } from "@inertiajs/react";
import ContactInfo from "@/Components/AdminSections/Common/ContactInfo";
import NameAndLogo from "@/Components/AdminSections/Common/NameAndLogo";
import OtherInfo from "@/Components/AdminSections/Common/OtherInfo";

const Admin = () => {
  const breadcrumbs = [
    {
      title: "Thông Tin Chung",
      href: "/admin/thong-tin-chung",
    },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Thông Tin Chung" />
      <div className="flex h-full flex-1 flex-col space-y-8 p-4">

        <div className="min-h-screen bg-background">
          {/* Admin Header */}
          <div className="bg-card">
            <div className="container mx-auto">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                    <Settings className="w-5 h-5 text-primary-foreground" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold">Nha Khoa 246 Admin</h1>
                    <p className="text-muted-foreground">Quản lý thông tin chung cho các trang</p>
                  </div>
                </div>
                {/* <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    <Eye className="w-4 h-4" />
                    Preview Site
                  </Button>
                </div> */}
              </div>
            </div>
          </div>

          <div className="container mx-auto px-4 py-8">
            <Tabs defaultValue="name" className="space-y-6">
              <TabsList className="grid w-full grid-cols-6">
                <TabsTrigger value="name">Tên công ty & Logo</TabsTrigger>
                <TabsTrigger value="contact">Thông tin liên hệ</TabsTrigger>
                <TabsTrigger value="other">Thông tin khác</TabsTrigger>
              </TabsList>

              <TabsContent value="name" className="space-y-6">
                <NameAndLogo />
              </TabsContent>

              <TabsContent value="contact" className="space-y-6">
                <ContactInfo />
              </TabsContent>

              <TabsContent value="other" className="space-y-6">
                <OtherInfo />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default Admin;