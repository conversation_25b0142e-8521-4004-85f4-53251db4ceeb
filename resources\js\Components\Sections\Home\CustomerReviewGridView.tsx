import { Card } from "@/Components/ui/card";
import { useState } from "react";
import MediaModal from "../../MediaModal";
import { Button } from "@/Components/ui/button";
import { Link } from "@inertiajs/react";
import { usePage } from "@inertiajs/react";
import { CustomerReview as CustomerReviewType, Title } from "@/types";

const CustomerReview = () => {
    const customerReviewTitle = usePage().props.customerReviewTitle as Title;
    const customerReviews = usePage().props.customerReviews as CustomerReviewType[];

    const [selectedMedia, setSelectedMedia] = useState<{
        type: "image" | "video";
        media_url: string;
        image?: string;
    } | null>(null);

    const [visibleCount, setVisibleCount] = useState(8);

    // const customers = [
    //     {
    //         id: 1,
    //         type: "video" as const,
    //         media_url: "2-pBkA0ue2c",
    //         image: "",
    //     },
    //     {
    //         id: 2,
    //         type: "image" as const,
    //         image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-1_cnqtsl.png",
    //     },
    //     {
    //         id: 3,
    //         type: "video" as const,
    //         media_url: "Oq0QcC1ifWw",
    //     },
    //     {
    //         id: 4,
    //         type: "image" as const,
    //         image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-2_qzgmbw.png",
    //     },
    //     {
    //         id: 5,
    //         type: "video" as const,
    //         media_url: "xq1Q7XPZGlw",
    //     },
    //     {
    //         id: 6,
    //         type: "image" as const,
    //         image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-3_clbifh.png",
    //     },
    //     {
    //         id: 7,
    //         type: "video" as const,
    //         media_url: "_hm5uCeRUL8",
    //     },
    //     {
    //         id: 8,
    //         type: "video" as const,
    //         media_url: "aAvREhNTA3E",
    //     },
    //     {
    //         id: 9,
    //         type: "image" as const,
    //         image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-4_eqeslr.png",
    //     },
    //     {
    //         id: 10,
    //         type: "video" as const,
    //         media_url: "QoYNKSFgbF8",
    //     },
    //     {
    //         id: 11,
    //         type: "video" as const,
    //         media_url: "caiPaptOSGk",
    //     },
    //     {
    //         id: 12,
    //         type: "image" as const,
    //         image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-5_uvajfb.png",
    //     },
    //     {
    //         id: 13,
    //         type: "video" as const,
    //         media_url: "5Y3pnSlqj38",
    //     },
    // ]

    // const customers = [
    //     {
    //         id: 1,
    //         name: "Vy",
    //         title: "Sinh viên đại học",
    //         image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-1_cnqtsl.png",
    //         testimonial: "Nha Khoa 246 đã là nơi chăm sóc răng miệng đáng tin cậy cho tôi suốt hơn 3 năm. Dịch vụ chuyên nghiệp và phong cách nhẹ nhàng khiến mỗi lần đến khám đều rất thoải mái",
    //         rating: 5,
    //         type: "image" as const,
    //         treatment: "Chăm sóc, vệ sinh răng miệng",
    //         media_url: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-1_cnqtsl.png",    
    //     },
    //     {
    //         id: 2,
    //         name: "Vy",
    //         title: "Sinh viên đại học",
    //         image: "https://www.youtube.com/embed/_hm5uCeRUL8?autoplay=0&mute=0&loop=1&playlist=_hm5uCeRUL8&controls=0&showinfo=0&rel=0&modestbranding=1",
    //         testimonial: "Nha Khoa 246 đã là nơi chăm sóc răng miệng đáng tin cậy cho tôi suốt hơn 3 năm. Dịch vụ chuyên nghiệp và phong cách nhẹ nhàng khiến mỗi lần đến khám đều rất thoải mái",
    //         rating: 5,
    //         type: "video" as const,
    //         treatment: "Chăm sóc, vệ sinh răng miệng",
    //         media_url: "_hm5uCeRUL8",
    //     },
    //     {
    //         id: 3,
    //         name: "Vy",
    //         title: "Sinh viên đại học",
    //         image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-2_qzgmbw.png",
    //         testimonial: "Nha Khoa 246 đã là nơi chăm sóc răng miệng đáng tin cậy cho tôi suốt hơn 3 năm. Dịch vụ chuyên nghiệp và phong cách nhẹ nhàng khiến mỗi lần đến khám đều rất thoải mái",
    //         rating: 5,
    //         type: "image" as const,
    //         treatment: "Chăm sóc, vệ sinh răng miệng",
    //         media_url: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-2_qzgmbw.png",    
    //     },
    //     {
    //         id: 4,
    //         name: "Chú Hảo",
    //         title: "Việt kiều Mỹ",
    //         testimonial: "Chất lượng điều trị tại Nha Khoa 246 thật sự xuất sắc. Bác sĩ Wong và đội ngũ đã thay đổi hoàn toàn nụ cười của tôi nhờ kỹ thuật chuyên sâu. Tôi hoàn toàn hài lòng với kết quả nhận được.",
    //         rating: 5,
    //         type: "video" as const,
    //         treatment: "Trồng răng trên Implant",
    //         media_url: "aAvREhNTA3E",    
    //     },
    //     {
    //         id: 5,
    //         name: "Vy",
    //         title: "Sinh viên đại học",
    //         image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-3_clbifh.png",
    //         testimonial: "Nha Khoa 246 đã là nơi chăm sóc răng miệng đáng tin cậy cho tôi suốt hơn 3 năm. Dịch vụ chuyên nghiệp và phong cách nhẹ nhàng khiến mỗi lần đến khám đều rất thoải mái",
    //         rating: 5,
    //         type: "image" as const,
    //         treatment: "Chăm sóc, vệ sinh răng miệng",
    //         media_url: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-3_clbifh.png",    
    //     },
    //     {
    //         id: 6,
    //         name: "Cô Dung",
    //         title: "Việt kiều Mỹ",
    //         image: "https://images.unsplash.com/photo-1494790108755-2616b332c265?auto=format&fit=crop&q=80",
    //         testimonial: "Tôi rất thích đến đây! Dịch vụ nha khoa nhi tuyệt vời. Nhân viên luôn tạo ra môi trường vui vẻ và thân thiện.",
    //         rating: 5,
    //         type: "video" as const,
    //         treatment: "Trồng răng trên Implant",
    //         media_url: "QoYNKSFgbF8",    
    //     },
    //     {
    //         id: 7,
    //         name: "Vy",
    //         title: "Sinh viên đại học",
    //         image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-4_eqeslr.png",
    //         testimonial: "Nha Khoa 246 đã là nơi chăm sóc răng miệng đáng tin cậy cho tôi suốt hơn 3 năm. Dịch vụ chuyên nghiệp và phong cách nhẹ nhàng khiến mỗi lần đến khám đều rất thoải mái",
    //         rating: 5,
    //         type: "image" as const,
    //         treatment: "Chăm sóc, vệ sinh răng miệng",
    //         media_url: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-4_eqeslr.png",    
    //     },
    //     {
    //         id: 8,
    //         name: "Chú Khoa",
    //         title: "Việt kiều Nauy",
    //         testimonial: "Mọi người cùng lắng nghe cảm nhận của chú về Nha khoa 246 và Trồng răng trên implant nhé 👍",
    //         rating: 5,
    //         type: "video" as const,
    //         treatment: "Trồng răng trên Implant",
    //         media_url: "caiPaptOSGk",    
    //     },
    //     {
    //         id: 9,
    //         name: "Vy",
    //         title: "Sinh viên đại học",
    //         image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-5_uvajfb.png",
    //         testimonial: "Nha Khoa 246 đã là nơi chăm sóc răng miệng đáng tin cậy cho tôi suốt hơn 3 năm. Dịch vụ chuyên nghiệp và phong cách nhẹ nhàng khiến mỗi lần đến khám đều rất thoải mái",
    //         rating: 5,
    //         type: "image" as const,
    //         treatment: "Chăm sóc, vệ sinh răng miệng",
    //         media_url: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/pv-5_uvajfb.png",    
    //     },
    //     {
    //         id: 10,
    //         name: "Cô Kim Cảnh",
    //         title: "Bán hàng",
    //         testimonial: 'Từng là một người rất sợ làm răng nhưng Cô đã vượt qua nỗi sợ của mình để chọn giải pháp "Trồng răng trên Implant" - Giải pháp trồng lại răng đã mất tối ưu nhất hiện nay 👍',
    //         rating: 5,
    //         type: "video" as const,
    //         treatment: "Trồng răng trên Implant",
    //         media_url: "5Y3pnSlqj38",    
    //         thumbnailUrl: ""
    //     }
    // ];

    const handleMediaClick = (customer: CustomerReviewType) => {
        // if (customer.type === "video") {
            setSelectedMedia({
                type: customer.type,
                media_url: customer.media_url ?? "",
                image: customer.image ?? "",
            });
        // }
    };

    const handleViewMore = () => {
        setVisibleCount(prev => Math.min(prev + 8, customerReviews.length));
    };

    const visibleCustomers = customerReviews.slice(0, visibleCount)

  return (
    <>
         <section className="py-20 bg-gray-50">
            <div className="container mx-auto px-4">
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        {customerReviewTitle?.main_title ?? "Khách Hàng Nói Gì Về Chúng Tôi"}
                    </h2>
                    <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        {customerReviewTitle?.subtitle ?? "Khám phá những chia sẻ chân thành từ những khách hàng đã tin tưởng và trải nghiệm dịch vụ tại Nha Khoa 246."}
                    </p>
                </div>

                {/* Grid Layout */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 max-w-7xl mx-auto mb-12">
                    {visibleCustomers.map((customer) => (
                    <Card 
                        key={customer.id} 
                        className="group cursor-pointer overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 shadow-lg"
                        onClick={() => handleMediaClick(customer)}
                    >
                        <div className="relative aspect-square overflow-hidden">
                        {(customer.type === "video") ? (
                            // <iframe 
                            //     src="https://www.youtube.com/embed/8DW0PJGaPiY?autoplay=1&mute=1&loop=1&playlist=8DW0PJGaPiY&controls=0&showinfo=0&rel=0&modestbranding=1" 
                            //     width="100%" 
                            //     height="350" 
                            //     allow="autoplay; encrypted-media"
                            //     allowFullScreen
                            //     className="pointer-events-none"
                            // />
                            <iframe
                                src={`https://www.youtube.com/embed/${customer.media_url}?autoplay=0&mute=0&loop=0&&controls=0&showinfo=0&rel=0&modestbranding=1`}
                                title="YouTube video player"
                                allow="autoplay; encrypted-media"
                                allowFullScreen
                                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                            />
                        ) : (
                            <img
                                src={customer.image}
                                alt={customer.id.toString()}
                                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                            />
                        )}
                        
                        {/* Overlay */}
                        <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            {/* {customer.type === "video" ? (
                            <div className="bg-red-500 rounded-full p-4 shadow-lg">
                                <Play className="w-8 h-8 text-white fill-white" />
                            </div>
                            ) : (
                            <div className="bg-emerald-600 rounded-full p-4 shadow-lg">
                                <Eye className="w-8 h-8 text-white" />
                            </div>
                            )} */}
                        </div>

                        {/* Video indicator */}
                        {/* {customer.type === "video" && (
                            <div className="absolute top-3 right-3 bg-red-500 rounded-full p-2">
                                <Play className="w-3 h-3 text-white fill-white" />
                            </div>
                        )} */}
                        </div>
                    </Card>
                    ))}
                </div>

                {/* View More Button */}
                {visibleCount < customerReviews.length && (
                    <div className="text-center">
                        <Button onClick={handleViewMore} className="rounded-full hover:shadow-lg hover:-translate-y-1">
                            Xem thêm chia sẻ khác
                        </Button>
                    </div>
                )}

                {/* Bottom Call to Action */}
                {visibleCount >= customerReviews.length && (
                    <div className="text-center mt-12">
                        <p className="text-gray-600 mb-4">Sẵn sàng bắt đầu hành trình thay đổi nụ cười của riêng bạn chưa?</p>
                        <Link href={"/contact"}>
                            <Button className="rounded-full hover:shadow-lg hover:-translate-y-1">
                                Đặt lịch tư vấn ngay bây giờ!
                            </Button>
                        </Link>
                    </div>
                )}
            </div>
        </section>

        <MediaModal
            isOpen={!!selectedMedia}
            onClose={() => setSelectedMedia(null)}
            type={selectedMedia?.type || "video"}
            title={""}
            description={""}
            image={selectedMedia?.image}
            media_url={selectedMedia?.media_url || ""}
            hideText
        />
        </>
  );
};

export default CustomerReview;
