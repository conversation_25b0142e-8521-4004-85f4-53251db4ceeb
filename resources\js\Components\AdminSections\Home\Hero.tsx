import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Image, Mail, MapPin, Phone, Plus, Save, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import { Textarea } from "@/Components/ui/textarea";
import { Separator } from "@/Components/ui/separator";
import { toast } from "sonner";
import { router, usePage } from "@inertiajs/react";
import { CommonInfo, HomeHero } from "@/types";

const Hero = () => {
    const homeHero = usePage().props.homeHero as HomeHero;
    const commonInfo = usePage().props.commonInfo as CommonInfo;

    const [heroData, setHeroData] = useState({
        id: homeHero?.id ?? 0,
        main_title: homeHero?.main_title ?? "Nụ Cười <PERSON>,",
        title_highlight: homeHero?.title_highlight ?? "Ưu Tiên Của Chúng Tôi", 
        subtitle: homeHero?.subtitle ?? "Trung tâm nha khoa hiện đại, đội ngũ bác sĩ tận tâm – nơi bạn luôn cảm thấy an toàn, thõa mái và được chăm sóc tận tình.",
        primary_button_text: homeHero?.primary_button_text ?? "Tìm hiểu ngay",
        background_images: homeHero?.background_images ?? [],
        background_images_mobile: homeHero?.background_images_mobile ?? [],
        contact_info: {
            phone: commonInfo?.phone ?? "0273.6588.988",
            phone2: commonInfo?.phone2 ?? "0939.809.246",
            location: commonInfo?.location ?? "166 Trương Định, khu phố 3, P. Long Thuận, tỉnh Đồng Tháp", 
            email: commonInfo?.email ?? "<EMAIL>", 
        }
    });

    useEffect(() => {
        if (!homeHero) return;

        setHeroData({
            ...homeHero,
            contact_info: {
                phone: commonInfo?.phone ?? "0273.6588.988",
                phone2: commonInfo?.phone2 ?? "0939.809.246",
                location: commonInfo?.location ?? "166 Trương Định, khu phố 3, P. Long Thuận, tỉnh Đồng Tháp", 
                email: commonInfo?.email ?? "<EMAIL>", 
            }
        });
    }, [homeHero, commonInfo]);

    const handleSave = () => {
        if (heroData.id !== 0) {
            router.put(
                route('home-hero.update', heroData.id), 
                heroData, 
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
            
        } else {
            router.post(route('home-hero.store'), 
                heroData,
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
        }
    };
    
    return (
        <Card>
            <CardHeader>
            <CardTitle className="flex items-center">
                <Image className="w-5 h-5 mr-2" />
                Hero trang chính
            </CardTitle>
            <CardDescription>Quản lý nội dung và hình ảnh của hero</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                    <div>
                        <Label htmlFor="main_title">Tiêu đề</Label>
                        <Input
                        id="main_title"
                        value={heroData.main_title}
                        onChange={(e) => setHeroData({...heroData, main_title: e.target.value})}
                        />
                    </div>
                    <div>
                        <Label htmlFor="title_highlight">Tiêu đề phụ</Label>
                        <Input
                        id="title_highlight"
                        value={heroData.title_highlight}
                        onChange={(e) => setHeroData({...heroData, title_highlight: e.target.value})}
                        />
                    </div>
                    <div>
                        <Label htmlFor="subtitle">Phụ đề</Label>
                        <Textarea
                        id="subtitle"
                        value={heroData.subtitle}
                        onChange={(e) => setHeroData({...heroData, subtitle: e.target.value})}
                        rows={3}
                        />
                    </div>
                    <div>
                        <Label htmlFor="primary-btn">Tên nút</Label>
                        <Input
                        id="primary-btn"
                        value={heroData.primary_button_text}
                        onChange={(e) => setHeroData({...heroData, primary_button_text: e.target.value})}
                        placeholder="Tên nút"
                        />
                    </div>
                </div>

                <div className="space-y-4">
                <h4 className="font-medium">Thông tin liên hệ <span className="text-destructive">*(Lấy từ Thông Tin Chung)</span></h4>
                <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                    <Phone className="w-4 h-4" />
                    <div className="flex flex-col md:flex-row w-full gap-2">
                    <Input
                        value={heroData.contact_info.phone}
                        onChange={(e) => setHeroData({
                        ...heroData, 
                        contact_info: {...heroData.contact_info, phone: e.target.value}
                        })}
                        disabled
                    />
                    <Input
                        value={heroData.contact_info.phone2}
                        onChange={(e) => setHeroData({
                        ...heroData, 
                        contact_info: {...heroData.contact_info, phone2: e.target.value}
                        })}
                        disabled
                    />
                    </div>
                    </div>
                    <div className="flex items-center space-x-2">
                    <Mail className="w-4 h-4" />
                    <Input
                        value={heroData.contact_info.email}
                        onChange={(e) => setHeroData({
                        ...heroData, 
                        contact_info: {...heroData.contact_info, email: e.target.value}
                        })}
                        disabled
                    />
                    </div>
                    <div className="flex items-center space-x-2">
                    <MapPin className="w-4 h-4" />
                    <Input
                        value={heroData.contact_info.location}
                        onChange={(e) => setHeroData({
                        ...heroData, 
                        contact_info: {...heroData.contact_info, location: e.target.value}
                        })}
                        disabled
                    />
                    </div>
                </div>
                </div>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 className="font-medium mb-4">Danh sách hình ảnh nền</h4>
                    <div className="space-y-3">
                        {(heroData.background_images.length > 0) && heroData.background_images.map((image, index) => (
                            <div key={index} className="flex items-center space-x-2">
                                <span className="text-sm text-muted-foreground w-8">#{index + 1}</span>
                                <Input
                                    value={image}
                                    onChange={(e) => {
                                    const updated = [...heroData.background_images];
                                    updated[index] = e.target.value;
                                    setHeroData({...heroData, background_images: updated});
                                    }}
                                    placeholder="Image URL"
                                />
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                    const updated = heroData.background_images.filter((_, i) => i !== index);
                                    setHeroData({...heroData, background_images: updated});
                                    }}
                                >
                                    <Trash2 className="w-4 h-4" />
                                </Button>
                            </div>
                        ))}
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setHeroData({
                            ...heroData, 
                            background_images: [...heroData.background_images, ""]
                            })}
                        >
                            <Plus className="w-4 h-4" />
                            Thêm hình ảnh
                        </Button>
                    </div>
                </div>

                <div className="border-t pt-6 md:border-l md:pl-6 md:border-t-0 md:pt-0">
                    <h4 className="font-medium mb-4">Danh sách hình ảnh nền Mobile</h4>
                    <div className="space-y-3">
                        {(heroData.background_images_mobile && heroData.background_images_mobile.length > 0) && heroData.background_images_mobile.map((image, index) => (
                            <div key={index} className="flex items-center space-x-2">
                                <span className="text-sm text-muted-foreground w-8">#{index + 1}</span>
                                <Input
                                    value={image}
                                    onChange={(e) => {
                                        const updated = [...heroData.background_images_mobile];
                                        updated[index] = e.target.value;
                                        setHeroData({...heroData, background_images_mobile: updated});
                                    }}
                                    placeholder="Image URL"
                                />
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                        const updated = heroData.background_images_mobile.filter((_, i) => i !== index);
                                        setHeroData({...heroData, background_images_mobile: updated});
                                    }}
                                >
                                    <Trash2 className="w-4 h-4" />
                                </Button>
                            </div>
                        ))}
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setHeroData({
                                ...heroData, 
                                background_images_mobile: [...heroData.background_images_mobile, ""]
                            })}
                        >
                            <Plus className="w-4 h-4" />
                            Thêm hình ảnh
                        </Button>
                    </div>
                </div>
            </div>

            <div className="flex justify-end">
                <Button onClick={() => handleSave()}>
                    <Save className="w-4 h-4" />
                    Lưu thay đổi
                </Button>
            </div>
            </CardContent>
        </Card>
    )
}

export default Hero;