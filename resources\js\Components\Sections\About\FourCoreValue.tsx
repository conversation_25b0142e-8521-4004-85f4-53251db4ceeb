
import { useState } from "react";
import {
  Carouse<PERSON>,
  Carouse<PERSON><PERSON><PERSON>,
  Carousel<PERSON>ontent,
  CarouselItem
} from "@/Components/ui/carousel";
import { useEffect } from "react";
import { usePage } from "@inertiajs/react";
import { Title } from "@/types";

// const core_values = [
//     {
//       id: 1,
//       title: "Honest",
//       description: "Transparent - Clear - No drawing disease",
//       image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/trung-thuc_ritqxg.jpg"
//     },
//     {
//       id: 2,
//       title: "Professional",
//       description: "Expert care with advanced techniques and modern equipment",
//       image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/tan-tam_x2ikag.jpg"
//     },
//     {
//       id: 3,
//       title: "Caring",
//       description: "Compassionate treatment focused on patient comfort and well-being",
//       image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/dong-cam_v8kmld.jpg"
//     },
//     {
//       id: 4,
//       title: "Excellence",
//       description: "Committed to delivering the highest quality dental care",
//       image: "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/ben-vung_flup9q.jpg"
//     }
// ];

const CoreValues = () => {
  const fourCoreValueTitle = usePage().props.fourCoreValueTitle as Title;
  const fourCoreValues = usePage().props.fourCoreValues as string[];

  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  const goToSlide = (index: number) => {
    if (api) {
      api.scrollTo(index);
    }
  };

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{fourCoreValueTitle?.main_title ?? "4 Giá Trị Cốt Lõi"}</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {fourCoreValueTitle?.subtitle ?? "Các giá trị cốt lõi mà chúng tôi luôn tuân thủ để mang lại cho bạn những trải nghiệm tốt nhất."}
          </p>
        </div>

        {/* Values List - Display all titles and descriptions */}
        {/* <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {core_values.map((value) => (
            <div key={value.id} className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-3">{value.title}</h3>
              <p className="text-gray-600 leading-relaxed">{value.description}</p>
            </div>
          ))}
        </div> */}

        {/* Image Carousel */}
        <div className="relative max-w-8xl mx-auto">
          <Carousel
            setApi={setApi}
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent className="-ml-2 md:-ml-4">
              {fourCoreValues.map((value, index) => (
                <CarouselItem key={index} className="pl-2 md:pl-4 md:basis-1/4">
                  <div className="relative group">
                    <img 
                      src={value} 
                      alt={value}
                      className="w-full h-full object-fill rounded-2xl shadow-lg group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute -inset-4 rounded-2xl -z-10 opacity-30"></div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            {/* <CarouselPrevious className="left-4" />
            <CarouselNext className="right-4" /> */}
          </Carousel>

          {/* Navigation Arrows */}
          {/* <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-3 shadow-lg transition-all duration-300 z-10"
          >
            <ChevronLeft className="w-6 h-6 text-gray-600" />
          </button>
          
          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-3 shadow-lg transition-all duration-300 z-10"
          >
            <ChevronRight className="w-6 h-6 text-gray-600" />
          </button> */}

          {/* Slide Indicators */}
          {/* <div className="flex justify-center mt-8 space-x-2">
            {Array.from({ length: count }).map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === current - 1
                    ? 'bg-blue-600' 
                    : 'bg-gray-400 hover:bg-blue-400'
                }`}
              />
            ))}
          </div> */}
        </div>
      </div>
    </section>
  );
};

export default CoreValues;
