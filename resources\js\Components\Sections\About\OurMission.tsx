const OurMission = () => {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <div className="space-y-8">
            <div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                Sứ Mệnh Của Chúng Tôi
              </h2>
              <div className="w-24 h-1 bg-emerald-600 mb-8"></div>
            </div>
            
            <div className="space-y-6">
              <p className="text-lg text-gray-600 leading-relaxed">
                Tại Nha Khoa 246, sứ mệnh của chúng tôi là nâng tầm trải nghiệm nha khoa hiện đại bằng cách kết hợp công nghệ tiên tiến với sự thấu hiểu và tận tâm. 
                Chúng tôi không chỉ điều trị răng – mà còn mang đến sự tự tin, sức khỏe và sự hài lòng lâu dài cho từng bệnh nhân.
              </p>
              <p className="text-lg text-gray-600 leading-relaxed">
                Từ năm 2020 đến nay, chúng tôi không ngừng đổi mới và mở rộng để phục vụ hàng trăm khách hàng, đồng hành cùng họ trên hành trình chăm sóc nụ cười một cách toàn diện và bền vững.
              </p>
            </div>
            
            {/* <Button>
                Tìm hiểu thêm về chúng tôi
            </Button> */}
          </div>
          
          {/* Video Section */}
          <div className="relative">
            <div className="relative overflow-hidden rounded-2xl shadow-2xl">
              <iframe 
                src="https://www.youtube.com/embed/8DW0PJGaPiY?autoplay=1&mute=1&loop=1&playlist=8DW0PJGaPiY&controls=0&showinfo=0&rel=0&modestbranding=1" 
                width="100%" 
                height="350" 
                allow="autoplay; encrypted-media"
                allowFullScreen
                className="pointer-events-none"
              />
              
              {/* Play button overlay (optional) */}
              {/* <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
                <div className="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                  <div className="w-0 h-0 border-l-8 border-l-emerald-600 border-t-4 border-t-transparent border-b-4 border-b-transparent ml-1"></div>
                </div>
              </div> */}
            </div>
            
            {/* Decorative elements */}
            <div className="absolute -top-6 -right-6 w-32 h-32 bg-emerald-100 rounded-full -z-10"></div>
            <div className="absolute -bottom-6 -left-6 w-24 h-24 bg-green-100 rounded-full -z-10"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OurMission;