import { Button } from "@/Components/ui/button";
import { Link, usePage } from "@inertiajs/react";
import { Phone, MapPin, Mail } from "lucide-react";

const Hero = () => {
  const address = usePage().props.address as string;

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-emerald-50 to-white">
      {/* Hero Content */}
      <div className="container mx-auto px-4 pt-24">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          <div className="space-y-8 animate-fade-in">
            <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
              Nụ Cười Của Bạn,
              <span className="text-emerald-600 block">Ưu Tiên Của Chúng Tôi</span>
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Trung tâm nha khoa hiện đại, đội ngũ bác sĩ tận tâm – nơi bạn luôn cảm thấy an toàn, thõa mái và được chăm sóc tận tình.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/about">
                <Button>
                  Tìm Hiểu Ngay
                </Button>
              </Link>
              {/* <Button variant="landing_outline">
                Gọi cho chúng tôi
              </Button> */}
            </div>
            
            {/* Quick Info */}
            <div className="grid grid-cols-1 md:grid-cols-1 gap-6 pt-8">
              <div className="flex items-center space-x-3 text-gray-700">
                <Phone className="w-5 h-5 text-emerald-600" />
                <span>0273.6588.988 - 0939.809.246</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-700">
                <MapPin className="w-5 h-5 text-emerald-600" />
                <span>{address}</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-700">
                <Mail className="w-5 h-5 text-emerald-600" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
          
          <div className="relative">
            <div className="relative z-10 bg-white rounded-2xl shadow-2xl p-8 animate-scale-in">
              <iframe 
                src="https://www.youtube.com/embed/v1nCVTHU6Bo?autoplay=1&mute=1&loop=1&playlist=v1nCVTHU6Bo&controls=0&showinfo=0&rel=0&modestbranding=1" 
                width="100%" 
                height="320" 
                allow="autoplay; encrypted-media"
                allowFullScreen
                className="object-cover rounded-xl mb-6 pointer-events-none"
              />
              <div className="space-y-4">
                <h3 className="text-2xl font-semibold text-gray-800">Dịch Vụ Nha Khoa Tốt Nhất</h3>
                <p className="text-gray-600">
                  Cơ sở nha khoa hiện đại và đội ngũ bác sĩ tận tâm. Chúng tôi cam kết mang lại trải nghiệm nha khoa an toàn và thoải mái cho bạn.
                </p>
                {/* <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>⭐⭐⭐⭐⭐ 4.9/5 Đánh Giá</span>
                  <span>500+ Đánh Giá Từ Khách hàng</span>
                </div> */}
              </div>
            </div>
            <div className="absolute -top-6 -right-6 w-72 h-72 bg-emerald-100 rounded-full -z-10"></div>
            <div className="absolute -bottom-6 -left-6 w-48 h-48 bg-green-100 rounded-full -z-10"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;