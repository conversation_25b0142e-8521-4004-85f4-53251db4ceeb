import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import { Textarea } from "@/Components/ui/textarea";
import { Badge } from "@/Components/ui/badge";
import { Calendar, Plus, Save, Settings, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Separator } from "@/Components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/Components/ui/select"
import { router, usePage } from "@inertiajs/react";
import { Equipment as EquipmentType, Title } from "@/types";

const Equipment = () => {
    const equipmentTitle = usePage().props.equipmentTitle as Title;
    const equipments = usePage().props.equipments as EquipmentType[];

    // Equipment State
    const [equipmentData, setEquipmentData] = useState({
        id: equipmentTitle?.id ?? 0,
        main_title: equipmentTitle?.main_title ?? "Your Smile,",
        subtitle: equipmentTitle?.subtitle ?? "Experience exceptional dental care with compassionate professionals dedicated to your comfort and oral health.",
        section_name: "equipments",
        equipments: equipments ?? []
    });

    // Equipment Functions
    const addActivity = () => {
        setEquipmentData({...equipmentData, 
            equipments: [...equipmentData.equipments, {
                id: "Id" + Date.now(),
                title: "Tên trang thiết bị",
                brief: "Tóm tắt về trang thiết bị",
                description: "Mô tả chi tiết về trang thiết bị",
                type: "image",
                image: "",
                media_url: ""
            }]
        });
    };

    const removeActivity = (index: number) => {
        setEquipmentData({...equipmentData, equipments: equipmentData.equipments.filter((_, i) => i !== index)});
    };

    const updateActivity = (index: number, field: string, value: any) => {
        const updated = [...equipmentData.equipments];
        updated[index] = { ...updated[index], [field]: value };
        setEquipmentData({...equipmentData, equipments: updated});
    };

    useEffect(() => {
        if (!equipmentTitle) return;

        setEquipmentData({
            equipments: equipments ?? [],
            ...equipmentTitle
        });
    }, [equipmentTitle, equipments]);

    const handleSave = () => {
        if (equipmentData.id !== 0) {
            router.put(
                route('equipments.update', equipmentData.id), 
                equipmentData as any, 
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
            
        } else {
            router.post(route('equipments.store'), 
                equipmentData as any,
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
        }
    };
    
    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center">
                        <Settings className="w-5 h-5 mr-2" />
                        Trang thiết bị
                    </span>
                    {/* <Button onClick={addActivity} size="sm">
                        <Plus className="w-4 h-4" />
                        Thêm hoạt động
                    </Button> */}
                </CardTitle>
                <CardDescription>Quản lý nội dung và hình ảnh của trang thiết bị</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="space-y-4">
                    <div>
                        <Label htmlFor="main_title">Tiêu đề</Label>
                        <Input
                        id="main_title"
                        value={equipmentData.main_title}
                        onChange={(e) => setEquipmentData({...equipmentData, main_title: e.target.value})}
                        />
                    </div>
                    <div>
                        <Label htmlFor="subtitle">Phụ đề</Label>
                        <Textarea
                        id="subtitle"
                        value={equipmentData.subtitle}
                        onChange={(e) => setEquipmentData({...equipmentData, subtitle: e.target.value})}
                        rows={3}
                        />
                    </div>
                </div>

                <Separator />
                
                <div className="space-y-4">
                    <h4 className="font-medium">Danh sách trang thiết bị</h4>
                    {equipmentData.equipments.map((activity, index) => (
                    <Card key={activity.id} className="relative">
                        <CardHeader className="pb-4">
                            <div className="flex items-center justify-between">
                            <Badge>Trang thiết bị {index + 1}</Badge>
                            <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => removeActivity(index)}
                            >
                                <Trash2 className="w-4 h-4" />
                            </Button>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid md:grid-cols-2 gap-4">
                                <div>
                                    <Label>Tên trang thiết bị</Label>
                                    <Input
                                    value={activity.title}
                                    onChange={(e) => updateActivity(index, 'title', e.target.value)}
                                    />
                                </div>
                                <div>
                                    <Label>Loại media</Label>
                                    <Select value={activity.type} onValueChange={(value) => updateActivity(index, 'type', value)}>
                                        <SelectTrigger className="w-full">
                                            <SelectValue placeholder="Loại media" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="image">Hình Ảnh</SelectItem>
                                            <SelectItem value="video">Video</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                            <div>
                                <Label>Mô tả ngắn</Label>
                                <Textarea
                                    value={activity.brief}
                                    onChange={(e) => updateActivity(index, 'brief', e.target.value)}
                                    rows={2}
                                />
                            </div>
                            <div>
                                <Label>Nội dung chi tiết</Label>
                                <Textarea
                                    value={activity.description}
                                    onChange={(e) => updateActivity(index, 'description', e.target.value)}
                                    rows={5}
                                />
                            </div>
                            <div>
                                <Label>Link ảnh</Label>
                                <Input
                                value={activity.image}
                                onChange={(e) => updateActivity(index, 'image', e.target.value)}
                                placeholder="Link ảnh"
                                />
                            </div>
                            {activity.type === "video" && (
                                <div>
                                    <Label>Id youtube video</Label>
                                    <Input
                                    value={activity.media_url}
                                    onChange={(e) => updateActivity(index, 'media_url', e.target.value)}
                                    placeholder="Id youtube video"
                                    />
                                </div>
                            )}
                            {/* {activity.type === "video" && (
                                <div>
                                    <Label>Video Thumbnail URL</Label>
                                    <Input
                                    value={activity.thumbnailUrl || ""}
                                    onChange={(e) => updateActivity(index, 'thumbnailUrl', e.target.value)}
                                    placeholder="Custom thumbnail for video"
                                    />
                                </div>
                            )} */}
                        </CardContent>
                    </Card>
                    ))}

                    <Button
                        variant="outline"
                        size="sm"
                        onClick={addActivity}
                    >
                        <Plus className="w-4 h-4" />
                        Thêm trang thiết bị
                    </Button>
                </div>
                
                <div className="flex justify-end">
                    <Button onClick={() => handleSave()}>
                        <Save className="w-4 h-4" />
                        Lưu thay đổi
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}

export default Equipment;