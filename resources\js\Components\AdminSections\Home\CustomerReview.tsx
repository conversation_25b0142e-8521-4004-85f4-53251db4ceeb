import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import { Textarea } from "@/Components/ui/textarea";
import { Badge } from "@/Components/ui/badge";
import { Users, Plus, Save, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Separator } from "@/Components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/Components/ui/select"
import { router, usePage } from "@inertiajs/react";
import { CustomerReview as CustomerReviewType, Title } from "@/types";

const CustomerReview = () => {
    const customerReviewTitle = usePage().props.customerReviewTitle as Title;
    const customerReviews = usePage().props.customerReviews as CustomerReviewType[];

    // Real Stories State
    const [stories, setStories] = useState({
        id: customerReviewTitle?.id ?? 0,
        main_title: customerReviewTitle?.main_title ?? "Những Chia Sẻ Của Khách Hàng",
        subtitle: customerReviewTitle?.subtitle ?? "Khám phá các hoạt động và dịch vụ nổi bật tại nha khoa của chúng tôi – từ chăm sóc răng miệng tổng quát đến các giải pháp thẩm mỹ hiện đại. Nha Khoa 246 luôn đặt chất lượng điều trị và sự hài lòng của bạn lên hàng đầu.",
        section_name: "customer_reviews",
        customer_reviews: customerReviews ?? []
    });

    // Stories Functions
    const addStory = () => {
        setStories({...stories, customer_reviews: [...stories.customer_reviews, {
            id: "Id" + Date.now(),
            type: "image",
            image: "",
            media_url: ""
        }]});
    };

    const removeStory = (index: number) => {
        setStories({...stories, customer_reviews: stories.customer_reviews.filter((_, i) => i !== index)});
    };

    const updateStory = (index: number, field: string, value: any) => {
        const updated = [...stories.customer_reviews];
        updated[index] = { ...updated[index], [field]: value };
        setStories({...stories, customer_reviews: updated});
    };

    useEffect(() => {
        if (!customerReviewTitle) return;

        setStories({
            ...customerReviewTitle,
            customer_reviews: customerReviews ?? []
        });
    }, [customerReviewTitle, customerReviews]);

    const handleSave = () => {
        if (stories.id !== 0) {
            router.put(
                route('customer-reviews.update', stories.id), 
                stories, 
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
            
        } else {
            router.post(route('customer-reviews.store'), 
                stories,
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
        }
    };

    return (
        <Card>
            <CardHeader>
            <CardTitle className="flex items-center justify-between">
                <span className="flex items-center">
                    <Users className="w-5 h-5 mr-2" />
                    Chia sẻ của khách hàng
                </span>
                {/* <Button onClick={addStory} size="sm">
                    <Plus className="w-4 h-4" />
                    Thêm chia sẻ
                </Button> */}
            </CardTitle>
            <CardDescription>Quản lý nội dung và hình ảnh chia sẻ của khách hàng</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="space-y-4">
                    <div>
                        <Label htmlFor="main_title">Tiêu đề</Label>
                        <Input
                        id="main_title"
                        value={stories.main_title}
                        onChange={(e) => setStories({...stories, main_title: e.target.value})}
                        />
                    </div>
                    <div>
                        <Label htmlFor="subtitle">Phụ đề</Label>
                        <Textarea
                        id="subtitle"
                        value={stories.subtitle}
                        onChange={(e) => setStories({...stories, subtitle: e.target.value})}
                        rows={3}
                        />
                    </div>
                </div>

                <Separator />
                
                <div className="space-y-4">
                    <h4 className="font-medium">Danh sách chia sẻ</h4>
                    {stories.customer_reviews.map((story, index) => (
                        <Card key={story.id} className="relative">
                        <CardHeader className="pb-4">
                            <div className="flex items-center justify-between">
                            <Badge>Chia sẻ {index + 1}</Badge>
                            <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => removeStory(index)}
                            >
                                <Trash2 className="w-4 h-4" />
                            </Button>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {/* <div className="grid md:grid-cols-3 gap-4">
                            <div>
                                <Label>Customer Name</Label>
                                <Input
                                value={story.name}
                                onChange={(e) => updateStory(index, 'name', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label>Job Title</Label>
                                <Input
                                value={story.title}
                                onChange={(e) => updateStory(index, 'title', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label>Treatment Type</Label>
                                <Input
                                value={story.treatment}
                                onChange={(e) => updateStory(index, 'treatment', e.target.value)}
                                />
                            </div>
                            </div>
                            <div>
                            <Label>Testimonial</Label>
                            <Textarea
                                value={story.testimonial}
                                onChange={(e) => updateStory(index, 'testimonial', e.target.value)}
                                rows={3}
                            />
                            </div> */}
                            {/* <div className="grid md:grid-cols-2 gap-4">
                            <div>
                                <Label>Media Type</Label>
                                <select
                                value={story.type}
                                onChange={(e) => updateStory(index, 'type', e.target.value)}
                                className="w-full px-3 py-2 border border-input rounded-md"
                                >
                                <option value="image">Image</option>
                                <option value="video">Video</option>
                                </select>
                            </div>
                            <div>
                                <Label>Rating</Label>
                                <select
                                value={story.rating}
                                onChange={(e) => updateStory(index, 'rating', parseInt(e.target.value))}
                                className="w-full px-3 py-2 border border-input rounded-md"
                                >
                                <option value={5}>5 Stars</option>
                                <option value={4}>4 Stars</option>
                                <option value={3}>3 Stars</option>
                                <option value={2}>2 Stars</option>
                                <option value={1}>1 Star</option>
                                </select>
                            </div>
                            <div>
                                <Label>Profile Image URL</Label>
                                <Input
                                value={story.image}
                                onChange={(e) => updateStory(index, 'image', e.target.value)}
                                placeholder="Customer profile image"
                                />
                            </div>
                            </div>
                            <div className="grid md:grid-cols-2 gap-4">
                            <div>
                                <Label>Media URL</Label>
                                <Input
                                value={story.media_url}
                                onChange={(e) => updateStory(index, 'media_url', e.target.value)}
                                placeholder="URL for testimonial image/video"
                                />
                            </div>
                            {story.type === "video" && (
                                <div>
                                <Label>Video Thumbnail URL</Label>
                                <Input
                                    value={story.thumbnailUrl || ""}
                                    onChange={(e) => updateStory(index, 'thumbnailUrl', e.target.value)}
                                    placeholder="Custom thumbnail for video"
                                />
                                </div>
                            )}
                            </div> */}

                            <div>
                                <Label>Loại media</Label>
                                <Select value={story.type} onValueChange={(value) => updateStory(index, 'type', value)}>
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Loại media" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="image">Image</SelectItem>
                                        <SelectItem value="video">Video</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            {story.type === "image" && (
                                <div>
                                    <Label>Link ảnh</Label>
                                    <Input
                                        value={story.image}
                                        onChange={(e) => updateStory(index, 'image', e.target.value)}
                                        placeholder="Link ảnh"
                                    />
                                </div>
                            )}
                            {story.type === "video" && (
                                <div>
                                    <Label>Id youtube video</Label>
                                    <Input
                                        value={story.media_url || ""}
                                        onChange={(e) => updateStory(index, 'media_url', e.target.value)}
                                        placeholder="Id youtube video"
                                    />
                                </div>
                            )}
                        </CardContent>
                        </Card>
                    ))}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={addStory}
                    >
                        <Plus className="w-4 h-4" />
                        Thêm chia sẻ
                    </Button>
                </div>

                <div className="flex justify-end">
                    <Button onClick={() => handleSave()}>
                    <Save className="w-4 h-4" />
                        Lưu thay đổi
                    </Button>
                </div>
                </CardContent>
        </Card>
    );
}

export default CustomerReview;