import { Card, CardContent } from "@/Components/ui/card";
import { CommonInfo, Title } from "@/types";
import { usePage } from "@inertiajs/react";
import { Phone, Mail, MapPin } from "lucide-react";


const ContactHero = () => {
  const address = usePage().props.address as string;
  const contactTitle = usePage().props.contactTitle as Title;
  const commonInfo = usePage().props.commonInfo as CommonInfo;
  const googleMapLink = commonInfo?.google_map_link ?? "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1644.887278376704!2d106.66971385209075!3d10.35803926750284!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31754f6d2bb44d39%3A0xc9f6e6b4756de08d!2sNha%20khoa%20246%20-%20Cty%20TNHH%20Usmile%20Dental!5e0!3m2!1svi!2s!4v1751099849683!5m2!1svi!2s";
  
  const contact_info = [
    {
      icon: <Phone className="w-6 h-6 text-emerald-600" />,
      title: "Điện thoại",
      details: [commonInfo?.phone ?? "0273.6588.988", commonInfo?.phone2 ?? "0939.809.246"]
    },
    {
      icon: <Mail className="w-6 h-6 text-green-600" />,
      title: "Email",
      details: [commonInfo?.email ?? "<EMAIL>"]
    },
    {
      icon: <MapPin className="w-6 h-6 text-red-600" />,
      title: "Địa chỉ",
      details: [commonInfo?.location ?? address]
    }
  ];

  return (
    <section id="contact" className="py-20 pt-32 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{contactTitle?.main_title ?? "Liên Hệ Với Chúng Tôi"}</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {contactTitle?.subtitle ?? "Bạn đã sẵn sàng đặt lịch hẹn? Hãy liên hệ với chúng tôi ngay hôm nay để bắt đầu hành trình hướng tới một nụ cười khỏe đẹp hơn."}
          </p>
        </div>
        
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Google Map */}
          <Card className="bg-white shadow-lg">
            {/* <CardHeader>
              <CardTitle className="text-2xl font-semibold">Find Us</CardTitle>
            </CardHeader> */}
            <CardContent className="p-0">
              <div className="w-full h-96 rounded-lg overflow-hidden">
                {/* <iframe  width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe> */}
                <iframe
                  src={googleMapLink}
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title="Nha khoa 246"
                />
              </div>
            </CardContent>
          </Card>
          
          {/* Contact Information */}
          <div className="space-y-8">
            <div className="grid grid-cols-1 gap-6">
              {contact_info.map((info, index) => (
                <Card key={index} className="bg-white hover:shadow-md transition-shadow">
                  <CardContent className="flex items-start space-x-4 p-6">
                    <div className="mt-1">{info.icon}</div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">{info.title}</h3>
                      {info.details.map((detail, idx) => (
                        <p key={idx} className="text-gray-600">{detail}</p>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactHero;