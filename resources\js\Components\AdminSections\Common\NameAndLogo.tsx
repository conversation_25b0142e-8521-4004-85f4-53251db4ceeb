import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title, CardDescription } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Image, Save } from "lucide-react";
import { useEffect, useState } from "react";
import { Input } from "@/Components/ui/input";
import { toast } from "sonner";
import { Label } from "@/Components/ui/label";
import { router, usePage } from "@inertiajs/react";
import { CommonInfo } from "@/types";

const NameAndLogo = () => {
    const commonInfo = usePage().props.commonInfo as CommonInfo;

    const [nameAndLogoData, setNameAndLogoData] = useState({
      id: commonInfo?.id ?? 0,
      phone: commonInfo?.phone ?? "0273.6588.988",
      phone2: commonInfo?.phone2 ?? "0939.809.246",
      location: commonInfo?.location ?? "166 Trương <PERSON>, khu <PERSON>h<PERSON> 3, <PERSON><PERSON>, tỉnh <PERSON>ồng <PERSON>", 
      email: commonInfo?.email ?? "<EMAIL>",
      google_map_link: commonInfo?.google_map_link ?? "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3924.8109477379166!2d106.67020109678955!3d10.356996300000015!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31754f002909cebd%3A0x48e8296554c936ed!2sNha%20Khoa%20246%20-USmile%20Dental%20Office!5e0!3m2!1svi!2s!4v1752676492429!5m2!1svi!2s",
      name: commonInfo?.name ?? "Nha Khoa 246",
      name2: commonInfo?.name2 ?? "USmile Dental",
      logo_link: commonInfo?.logo_link ?? "https://res.cloudinary.com/dtb2p8vbi/image/upload/q_auto,f_auto/logo_xorkjq.jpg",
      service_list: commonInfo?.service_list ?? [
        'Nhổ Răng Khôn',
        'Chỉnh Nha',
        'Trồng Răng Trên Implant',
        'Nha Khoa Thẩm Mỹ'
      ],
    });

    useEffect(() => {
        if (!commonInfo) return;

        setNameAndLogoData(commonInfo);
    }, [commonInfo]);

    const handleSave = () => {
        if (nameAndLogoData.id !== 0) {
            router.put(
                route('common-infos.update', nameAndLogoData.id), 
                nameAndLogoData, 
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
            
        } else {
            router.post(route('common-infos.store'), 
                nameAndLogoData,
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
        }
    };
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Image className="w-5 h-5 mr-2" />
            Tên công ty & Logo
          </CardTitle>
          <CardDescription>Quản lý thông tin tên công ty và logo</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-1 gap-6">
            <div className="space-y-4">
                <div>
                    <Label htmlFor="name">Tên công ty</Label>
                    <Input
                    id="name"
                    value={nameAndLogoData.name}
                    onChange={(e) => setNameAndLogoData({...nameAndLogoData, name: e.target.value})}
                    />
                </div>
                <div>
                    <Label htmlFor="name2">Tên công ty tiếng anh</Label>
                    <Input
                    id="name2"
                    value={nameAndLogoData.name2}
                    onChange={(e) => setNameAndLogoData({...nameAndLogoData, name2: e.target.value})}
                    />
                </div>
                 <div>
                    <Label htmlFor="logo_link">Link logo</Label>
                    <Input
                    id="logo_link"
                    value={nameAndLogoData.logo_link}
                    onChange={(e) => setNameAndLogoData({...nameAndLogoData, logo_link: e.target.value})}
                    />
                </div>
                <div>
                  <div className="w-40 h-40 rounded-full flex items-center justify-center">
                    <img src={nameAndLogoData.logo_link} alt="logo" className="rounded-full" />
                  </div>
                </div>
            </div>
          </div>
        
          <div className="flex justify-end">
            <Button onClick={() => handleSave()}>
              <Save className="w-4 h-4" />
              Lưu thay đổi
            </Button>
          </div>
        </CardContent>
      </Card>
    )
}

export default NameAndLogo;