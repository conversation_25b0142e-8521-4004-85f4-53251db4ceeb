import { Dialog, DialogClose, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/Components/ui/dialog";
import { X, } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";

interface MediaModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: "image" | "video";
  title: string;
  description?: string;
  media_url: string;
  image?: string;
  hideText?: boolean;
}

const MediaModal = ({ isOpen, onClose, type, title, description, media_url, image, hideText }: MediaModalProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  console.log(type, media_url);
  
  useEffect(() => {
    if (isOpen) {
      setIsPlaying(false);
    }
  }, [isOpen]);

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogClose className="text-white" />
      <DialogContent className="max-w-4xl w-full max-h-[80vh] p-0 overflow-hidden overflow-y-auto">
        {!hideText && (
          <>
            <DialogHeader className="p-6 pb-0">
            <DialogTitle className="text-2xl font-bold text-gray-900">{title}</DialogTitle>
              {description && <p className="text-gray-600 mt-4 whitespace-pre-wrap">{description}</p>}
            </DialogHeader>
            <DialogClose asChild>
              <button className="absolute right-4 top-4 z-50 text-muted-foreground">
                <X className="h-4 w-4" />
              </button>
            </DialogClose>
          </>
        )}
       
        <div className="flex-1 relative bg-black rounded-b-lg overflow-hidden">
          {type === "image" ? (
            <div className="w-auto h-auto flex items-center justify-center p-4">
              <img 
                src={image} 
                alt={title}
                className={cn("max-w-full max-h-[70vh] object-contain rounded-lg shadow-lg",
                  hideText && "h-[70vh]",
                )}
              />
            </div>
          ) : (
            <div className="relative w-full h-full py-1">
              <iframe 
                src={`https://www.youtube.com/embed/${media_url}?autoplay=0&mute=0&loop=0&controls=1&showinfo=0&rel=0&modestbranding=1`}
                width="100%" 
                // height="420" 
                allow="autoplay; encrypted-media"
                allowFullScreen
                className="object-contain h-[70vh]"
              />
              {/* <video
                ref={videoRef}
                className="w-full h-full object-contain"
                poster={thumbnailUrl}
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onEnded={() => setIsPlaying(false)}
                onError={(e) => {
                  console.error('Video failed to load:', media_url, e);
                }}
                onLoadStart={() => {
                  console.log('Video loading started:', media_url);
                }}
              >
                <source src={media_url} type="video/mp4" />
                Your browser does not support the video tag or the video file could not be loaded.
              </video> */}
              
              {/* Video Controls Overlay */}
              {/* <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="bg-white/20 hover:bg-white/30 text-white"
                      onClick={togglePlay}
                    >
                      {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="icon"
                      className="bg-white/20 hover:bg-white/30 text-white"
                      onClick={toggleMute}
                    >
                      {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                    </Button>
                  </div>
                </div>
              </div> */}
              
              {/* Play button overlay when paused */}
              {/* {!isPlaying && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="bg-white/20 hover:bg-white/30 text-white w-20 h-20 rounded-full"
                    onClick={togglePlay}
                  >
                    <Play className="w-10 h-10 ml-1" />
                  </Button>
                </div>
              )} */}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MediaModal;