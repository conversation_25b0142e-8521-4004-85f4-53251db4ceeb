import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title, CardDescription } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Image, Plus, Save, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import { Textarea } from "@/Components/ui/textarea";
import { Separator } from "@/Components/ui/separator";
import { toast } from "sonner";
import { router, usePage } from "@inertiajs/react";
import { AboutHero } from "@/types";

const Hero = () => {
  const aboutHero = usePage().props.aboutHero as AboutHero;

    // Hero Section State
  const [aboutData, setAboutData] = useState(
    {
        id: aboutHero?.id ?? 0,
        main_title: aboutHero?.main_title ?? "Nha Khoa 246",
        subtitle: aboutHero?.subtitle ?? "Trung tâm nha khoa hiện đại, đội ngũ bác sĩ tận tâm – nơi bạn luôn cảm thấy an toàn, thõa mái và được chăm sóc tận tình.",
        primary_button_text: aboutHero?.primary_button_text ?? "Tìm hiểu ngay",
        background_video: aboutHero?.background_video ?? "",
        stats: aboutHero?.stats ?? [
          { number: "15+", label: "Years of Excellence" },
          { number: "500+", label: "Happy Patients" },
          { number: "4.9★", label: "Patient Rating" }
        ]
    }
  );

  const handleHeroUpdate = (field: string, value: any) => {
    setAboutData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleStatUpdate = (index: number, field: string, value: string) => {
    const updatedStats = [...aboutData.stats];
    updatedStats[index] = { ...updatedStats[index], [field]: value };
    setAboutData(prev => ({ ...prev, stats: updatedStats }));
  };

  const addStat = () => {
    setAboutData(prev => ({
      ...prev,
      stats: [...prev.stats, { number: "", label: "" }]
    }));
  };

  const removeStat = (index: number) => {
    setAboutData(prev => ({
      ...prev,
      stats: prev.stats.filter((_, i) => i !== index)
    }));
  };

  const updateBackgroundVideo = (value: string) => {
    setAboutData(prev => ({ ...prev, background_video: value }));
  };

    useEffect(() => {
        if (!aboutHero) return;

        setAboutData(aboutHero);
    }, [aboutHero]);

    const handleSave = () => {
        if (aboutData.id !== 0) {
            router.put(
                route('about-hero.update', aboutData.id), 
                aboutData, 
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
            
        } else {
            router.post(route('about-hero.store'), 
                aboutData,
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
        }
    };
    
    return (
        <Card>
            <CardHeader>
            <CardTitle className="flex items-center">
                <Image className="w-5 h-5 mr-2" />
                Hero trang giới thiệu
            </CardTitle>
            <CardDescription>Quản lý nội dung và video của hero</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
            <div className="grid md:grid-cols-1 gap-6">
                <div className="space-y-4">
                    <div>
                        <Label htmlFor="main_title">Tiêu đề</Label>
                        <Input
                        id="main_title"
                        value={aboutData.main_title}
                        onChange={(e) => setAboutData({...aboutData, main_title: e.target.value})}
                        />
                    </div>
                    <div>
                        <Label htmlFor="subtitle">Phụ đề</Label>
                        <Textarea
                        id="subtitle"
                        value={aboutData.subtitle}
                        onChange={(e) => setAboutData({...aboutData, subtitle: e.target.value})}
                        rows={3}
                        />
                    </div>
                    
                    <div>
                        <Label htmlFor="primary-btn">Tên nút</Label>
                        <Input
                        id="primary-btn"
                        value={aboutData.primary_button_text}
                        onChange={(e) => handleHeroUpdate("primary_button_text", e.target.value)}
                        placeholder="Tên nút"
                        />
                    </div>
                    
                    <div>
                        <Label className="primary-btn">Video nền</Label>
                        <Input
                        value={aboutData.background_video}
                        onChange={(e) => updateBackgroundVideo(e.target.value)}
                        placeholder="Video URL"
                        />
                    </div>
                </div>
            </div>

            <Separator />
            
            {/* Statistics */}
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                <Label className="text-base font-semibold">Thống kê</Label>
                {/* <Button onClick={addStat} size="sm" variant="outline">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Stat
                </Button> */}
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {aboutData.stats.map((stat, index) => (
                    <Card key={index} className="p-4">
                    <div className="space-y-3">
                        <div className="flex justify-between items-center">
                        <Label className="text-sm font-medium">Thống kê {index + 1}</Label>
                        {/* <Button 
                            onClick={() => removeStat(index)} 
                            size="sm" 
                            variant="ghost"
                            disabled={aboutData.stats.length <= 1}
                        >
                            <Trash2 className="w-4 h-4" />
                        </Button> */}
                        </div>
                        <Input
                        value={stat.number}
                        onChange={(e) => handleStatUpdate(index, "number", e.target.value)}
                        placeholder="Number (e.g., 15+)"
                        />
                        <Input
                        value={stat.label}
                        onChange={(e) => handleStatUpdate(index, "label", e.target.value)}
                        placeholder="Label (e.g., Years of Excellence)"
                        />
                    </div>
                    </Card>
                ))}
                </div>
            </div>

            <div className="flex justify-end">
                <Button onClick={() => handleSave()}>
                    <Save className="w-4 h-4" />
                    Lưu thay đổi
                </Button>
            </div>
            </CardContent>
        </Card>
    )
}

export default Hero;