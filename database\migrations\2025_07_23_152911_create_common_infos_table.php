<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('common_infos', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('name2')->nullable();
            $table->string('logo_link')->nullable();
            $table->string('phone')->nullable();
            $table->string('phone2')->nullable();
            $table->string('location')->nullable();
            $table->string('email')->nullable();
            $table->text('google_map_link')->nullable();
            $table->json('service_list')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('common_infos');
    }
};
