<?php

namespace App\Http\Controllers;

use App\Models\SectionTitle;
use Illuminate\Http\Request;

class SectionTitleController extends Controller
{
    public function store(Request $request)
    {
        SectionTitle::create($request->all());
        return back();
    }
    public function update(Request $request, SectionTitle $sectionTitle)
    {
        $sectionTitle->update($request->all());
        return back();
    }
}
