<?php

namespace App\Http\Controllers;

use App\Models\AboutHero;
use App\Models\CommonInfo;
use App\Models\Equipment;
use App\Models\FourCoreValue;
use App\Models\OurMission;
use App\Models\SectionTitle;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AboutController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Admin/GioiThieu', [
            'aboutHero' => AboutHero::first(),
            'fourCoreValueTitle' => SectionTitle::where('section_name', 'four_core_values')->first(),
            'fourCoreValues' => FourCoreValue::first() ? FourCoreValue::first()->core_values : [],
            'ourMissionTitle' => SectionTitle::where('section_name', 'our_missions')->first(),
            'ourMissions' => OurMission::first() ? OurMission::first()->doctors : [],
            'equipmentTitle' => SectionTitle::where('section_name', 'equipments')->first(),
            'equipments' => Equipment::all(),
            'commonInfo' => CommonInfo::first(),
        ]);
    }
}
