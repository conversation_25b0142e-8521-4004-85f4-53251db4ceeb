import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Save, Mail, MapPin, Phone, Link } from "lucide-react";
import { useEffect, useState } from "react";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import { Textarea } from "@/Components/ui/textarea";
import { toast } from "sonner";
import { router, usePage } from "@inertiajs/react";
import { CommonInfo, Title } from "@/types";

const Contact = () => {
    const contactTitle = usePage().props.contactTitle as Title;
    const commonInfo = usePage().props.commonInfo as CommonInfo;

    // Contact Section State
    const [contactData, setContactData] = useState<Title & { contact_info: Partial<CommonInfo> }>({
        id: contactTitle?.id ?? 0,
        main_title: contactTitle?.main_title ?? "<PERSON>ên <PERSON>ớ<PERSON>",
        subtitle: contactTitle?.subtitle ?? "Bạn đã sẵn sàng đặt lịch hẹn? Hãy liên hệ với chúng tôi ngay hôm nay để bắt đầu hành trình hướng tới một nụ cười khỏe đẹp hơn.",
        section_name: "contacts",
        contact_info: commonInfo ?? {} as CommonInfo,
    });
    
    useEffect(() => {
        if (!contactTitle) return;

        setContactData({
            ...contactTitle,
            contact_info: commonInfo ?? {} as CommonInfo
        });
    }, [contactTitle, commonInfo]);

    const handleSave = () => {
        if (contactData.id !== 0) {
            router.put(
                route('contacts.update', contactData.id), 
                contactData as any, 
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
            
        } else {
            router.post(route('contacts.store'), 
                contactData as any,
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
        }
    };
    
    return (
        <Card>
            <CardHeader>
            <CardTitle className="flex items-center">
                <Mail className="w-5 h-5 mr-2" />
                Liên Hệ
            </CardTitle>
            <CardDescription>Quản lý nội dung liên hệ</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
            <div className="grid md:grid-cols-1 gap-6">
                <div className="space-y-4">
                <div>
                    <Label htmlFor="main_title">Tiêu đề</Label>
                    <Input
                    id="main_title"
                    value={contactData.main_title}
                    onChange={(e) => setContactData({...contactData, main_title: e.target.value})}
                    />
                </div>
                <div>
                    <Label htmlFor="subtitle">Phụ đề</Label>
                    <Textarea
                    id="subtitle"
                    value={contactData.subtitle}
                    onChange={(e) => setContactData({...contactData, subtitle: e.target.value})}
                    rows={3}
                    />
                </div>
                </div>

                <div className="space-y-2">
                {/* <h4 className="font-medium">Số Điện Thoại, Email, Địa Chỉ</h4> */}
                <Label htmlFor="contact_info">Thông tin liên hệ <span className="text-destructive">*(Lấy từ Thông Tin Chung)</span></Label>
                <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                    <Phone className="w-4 h-4" />
                    <div className="flex flex-col md:flex-row w-full gap-2">
                        <Input
                            value={contactData.contact_info.phone}
                            onChange={(e) => setContactData({
                                ...contactData, 
                                contact_info: {...contactData.contact_info, phone: e.target.value}
                            })}
                            placeholder="Số điện thoại 1"
                            disabled
                        />
                        <Input
                            value={contactData.contact_info.phone2}
                            onChange={(e) => setContactData({
                                ...contactData, 
                                contact_info: {...contactData.contact_info, phone2: e.target.value}
                            })}
                            placeholder="Số điện thoại 2"
                            disabled
                        />
                    </div>
                    </div>
                    <div className="flex items-center space-x-2">
                    <Mail className="w-4 h-4" />
                    <Input
                        value={contactData.contact_info.email}
                        onChange={(e) => setContactData({
                        ...contactData, 
                        contact_info: {...contactData.contact_info, email: e.target.value}
                        })}
                        placeholder="Email"
                        disabled
                    />
                    </div>
                    <div className="flex items-center space-x-2">
                    <MapPin className="w-4 h-4" />
                    <Input
                        value={contactData.contact_info.location}
                        onChange={(e) => setContactData({
                        ...contactData, 
                        contact_info: {...contactData.contact_info, location: e.target.value}
                        })}
                        placeholder="Địa chỉ"
                        disabled
                    />
                    </div>
                </div>
                </div>

                <div className="space-y-2">
                <Label htmlFor="contact_info">Google Map <span className="text-destructive">*(Lấy từ Thông Tin Chung)</span></Label>
                {/* <h4 className="font-medium">Link Địa chỉ GG Map</h4> */}
                <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                    <Link className="w-4 h-4" />
                    <Input
                        value={contactData.contact_info.google_map_link}
                        onChange={(e) => setContactData({
                        ...contactData, 
                        contact_info: {...contactData.contact_info, google_map_link: e.target.value}
                        })}
                        disabled
                    />
                    </div>
                    <div className="w-full h-60 rounded-lg overflow-hidden">
                        <iframe
                            src={contactData.contact_info.google_map_link}
                            width="100%"
                            height="100%"
                            style={{ border: 0 }}
                            allowFullScreen
                            loading="lazy"
                            referrerPolicy="no-referrer-when-downgrade"
                            title="Nha khoa 246"
                            className="pointer-events-none"
                        />
                    </div>
                    </div>
                </div>
            </div>

            <div className="flex justify-end">
                <Button onClick={() => handleSave()}>
                    <Save className="w-4 h-4 mr-2" />
                    Lưu thay đổi
                </Button>
            </div>
            </CardContent>
        </Card>
    )
}

export default Contact;