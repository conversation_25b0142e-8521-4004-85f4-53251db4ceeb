import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/Components/ui/card";
import { Smile, Shield, Zap, Heart, Star, Users } from "lucide-react";

const Services = () => {
  const services = [
    {
      icon: <Smile className="w-8 h-8 text-emerald-600" />,
      title: "Nha Khoa Tổng Quát",
      description: "Dịch vụ nha khoa bao gồm các dịch vụ như: rửa mặt, l<PERSON>y trắng răng, điều trị sâu răng.",
      features: ["Kiểm tra định kỳ", "Rửa mặt răng", "Trị sâu răng"]
    },
    {
      icon: <Star className="w-8 h-8 text-green-600" />,
      title: "Nha Khoa Nhân Tạo",
      description: "Trang điểm răng bằng veneer, trắng răng bằng phương pháp lấy trắng răng.",
      features: ["Lấy trắng răng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> điểm răng"]
    },
    {
      icon: <Shield className="w-8 h-8 text-purple-600" />,
      title: "Nha Khoa Chỉnh Hình",
      description: "Dịch vụ nha khoa chỉnh hình bao gồm các dịch vụ như: lấy trắng răng, ghép răng, ghép implant.",
      features: ["Lấy trắng răng", "Ghép răng", "Ghép implant"]
    },
    {
      icon: <Zap className="w-8 h-8 text-orange-600" />,
      title: "Nha Khoa Ngoại Trú",
      description: "Dịch vụ nha khoa ngoại trú bao gồm các dịch vụ như: lấy trắng răng, ghép răng, ghép implant.",
      features: ["Lấy trắng răng", "Ghép răng", "Ghép implant"]
    },
    {
      icon: <Heart className="w-8 h-8 text-red-600" />,
      title: "Nha Khoa Trẻ Em",
      description: "Dịch vụ nha khoa trẻ em bao gồm các dịch vụ như: lấy trắng răng, ghép răng, ghép implant.",
      features: ["Lấy trắng răng", "Ghép răng", "Ghép implant"]
    },
    {
      icon: <Users className="w-8 h-8 text-teal-600" />,
      title: "Nha Khoa Gia Đình",
      description: "Dịch vụ nha khoa gia đình bao gồm các dịch vụ như: lấy trắng răng, ghép răng, ghép implant.",
      features: ["Lấy trắng răng", "Ghép răng", "Ghép implant"]
    }
  ];

  return (
    <section id="services" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Dịch Vụ</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Dịch vụ nha khoa tốt nhất với trung tâm nha khoa hiện đại và đội ngũ bác sĩ tận tâm. 
            Chúng tôi cam kết mang lại trải nghiệm nha khoa thoải mái và thoải mái cho bạn.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index} className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1 bg-white">
              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-4">
                  {service.icon}
                </div>
                <CardTitle className="text-xl font-semibold">{service.title}</CardTitle>
                <CardDescription className="text-gray-600">
                  {service.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {service.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-sm text-gray-700">
                      <div className="w-2 h-2 bg-emerald-600 rounded-full mr-3"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;