<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AboutHero extends Model
{
    protected $table = 'about_heroes';

    protected $fillable = [
        'main_title',
        'subtitle',
        'primary_button_text',
        'background_video',
        'stats',
    ];

    protected $casts = [
        'stats' => 'array',
    ];
    // protected $hidden = [
    //     'created_at',
    //     'updated_at',
    // ];
    // protected $appends = [
    //     'stats',
    // ];
    // public function getStatsAttribute()
    // {
    //     return json_decode($this->attributes['stats'], true);
    // }
    // public function setStatsAttribute($value)
    // {
    //     $this->attributes['stats'] = json_encode($value);
    // }

}
