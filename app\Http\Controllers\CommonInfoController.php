<?php

namespace App\Http\Controllers;

use App\Models\CommonInfo;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CommonInfoController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Admin/ThongTinChung', [
            'commonInfo' => CommonInfo::first(),
        ]);
    }
    
    public function store(Request $request)
    {
        CommonInfo::create($request->all());
        return back();
    }
    public function update(Request $request, CommonInfo $commonInfo)
    {
        $commonInfo->update($request->all());
        return back();
    }
}
