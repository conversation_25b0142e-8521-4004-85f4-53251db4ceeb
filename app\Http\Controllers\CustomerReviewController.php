<?php

namespace App\Http\Controllers;

use App\Models\CustomerReview;
use App\Models\SectionTitle;
use Illuminate\Http\Request;

class CustomerReviewController extends Controller
{
    public function store(Request $request)
    {
        SectionTitle::create($request->all());
        $this->handleCustomerReviews($request);
        return back();
    }
    public function update(Request $request, SectionTitle $sectionTitle)
    {
        $sectionTitle->update($request->all());
        $this->handleCustomerReviews($request);
        return back();
    }

    public function handleCustomerReviews(Request $request)
    {
        $customerReviews = $request['customer_reviews'];

        // Filter out only valid integer IDs (existing records)
        $existingIds = array_filter(array_map(fn($customerReview) =>
            is_int($customerReview['id']) ? $customerReview['id'] : null
        , $customerReviews));

        // Delete records that are not in the current list of existing IDs
        if (!empty($existingIds)) {
            CustomerReview::whereNotIn('id', $existingIds)->delete();
        } else {
            // If no existing IDs, delete all records
            CustomerReview::truncate();
        }

        foreach ($customerReviews as $customerReview) {
            if (is_int($customerReview['id'])) {
                // Update existing record
                $existingRecord = CustomerReview::find($customerReview['id']);
                if ($existingRecord) {
                    $existingRecord->update($customerReview);
                }
            } else {
                // Create new record (remove the temporary ID)
                unset($customerReview['id']);
                CustomerReview::create($customerReview);
            }
        }
    }
}
