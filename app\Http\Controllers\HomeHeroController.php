<?php

namespace App\Http\Controllers;

use App\Models\HomeHero;
use Illuminate\Http\Request;

class HomeHeroController extends Controller
{
    public function store(Request $request)
    {
        HomeHero::create($request->all());
        return back();
    }
    public function update(Request $request, HomeHero $homeHero)
    {
        $homeHero->update($request->except('contact_info'));
        return back();
    }
}
