import { useState } from "react";
import { <PERSON><PERSON> } from "@/Components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/Components/ui/tabs";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import AppLayout from "@/Layouts/AppLayout";
import { Head } from "@inertiajs/react";
import { Badge } from "@/Components/ui/badge";
import Hero from "@/Components/AdminSections/About/Hero";
import FourCoreValue from "@/Components/AdminSections/About/FourCoreValue";
import OurMission from "@/Components/AdminSections/About/OurMission";
import Equipment from "@/Components/AdminSections/About/Equipment";

const AboutAdmin = () => {
  // Hero Section State
  const [heroData, setHeroData] = useState({
    title: "About Us",
    subtitle: "Discover the story behind Alba Dental - where cutting-edge technology meets compassionate care to create exceptional dental experiences for every patient.",
    primary_button_text: "Our Services",
    secondaryButtonText: "Contact Us",
    background_videos: [
      "https://assets.mixkit.co/videos/preview/mixkit-dentist-examining-a-patient-44568-large.mp4",
      "https://assets.mixkit.co/videos/preview/mixkit-dental-consultation-44567-large.mp4"
    ],
    stats: [
      { number: "15+", label: "Years of Excellence" },
      { number: "500+", label: "Happy Patients" },
      { number: "4.9★", label: "Patient Rating" }
    ]
  });

  const breadcrumbs = [
    {
      title: "Trang Giới Thiệu",
      href: "/admin/gioi-thieu",
    },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Trang Giới Thiệu" />
      <div className="flex h-full flex-1 flex-col space-y-8 p-4">

        <div className="min-h-screen bg-background">
         {/* Admin Header */}
          <div className="bg-card">
            <div className="container mx-auto">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                    <Settings className="w-5 h-5 text-primary-foreground" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold">Nha Khoa 246 Admin</h1>
                    <p className="text-muted-foreground">Quản lý nội dung trang giới thiệu</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" onClick={() => window.open('/about', '_blank')}>
                    <Eye className="w-4 h-4" />
                    Xem Trang Giới Thiệu
                  </Button>
                  {/* <Badge variant="secondary">Live</Badge> */}
                </div>
              </div>
            </div>
          </div>

          <div className="container mx-auto px-4 py-8">
            <Tabs defaultValue="hero" className="space-y-6">
              <TabsList className="grid w-full grid-cols-1 lg:grid-cols-4">
                <TabsTrigger value="hero">Hero</TabsTrigger>
                <TabsTrigger value="values">4 Giá trị cốt lõi</TabsTrigger>
                <TabsTrigger value="mission">Sứ mệnh của chúng tôi</TabsTrigger>
                <TabsTrigger value="equipment">Trang thiết bị</TabsTrigger>
              </TabsList>

              <TabsContent value="hero" className="space-y-6">
                <Hero />
              </TabsContent>

              <TabsContent value="mission" className="space-y-6">
                <OurMission />
              </TabsContent>

              <TabsContent value="values" className="space-y-6">
                <FourCoreValue />
              </TabsContent>

              <TabsContent value="equipment" className="space-y-6">
                <Equipment />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default AboutAdmin;