<?php

namespace App\Http\Controllers;

use App\Models\CommonInfo;
use App\Models\SectionTitle;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Application;

class ContactClientController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Site/Contact', [
            'contactTitle' => SectionTitle::where('section_name', 'contacts')->first(),
            'commonInfo' => CommonInfo::first(),
        
            'canLogin' => Route::has('login'),
            'canRegister' => Route::has('register'),
            'laravelVersion' => Application::VERSION,
            'phpVersion' => PHP_VERSION,
        ]);
    }
}
