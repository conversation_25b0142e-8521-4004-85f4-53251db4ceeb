import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/Components/ui/card";
import { Button } from "@/Components/ui/button";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import { Textarea } from "@/Components/ui/textarea";
import { Badge } from "@/Components/ui/badge";
import { Calendar, Plus, Save, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Separator } from "@/Components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/Components/ui/select"
import { router, usePage } from "@inertiajs/react";
import { Title, Activity } from "@/types";

const Activities = () => {
    const activitiesTitle = usePage().props.activitiesTitle as Title;
    const activities = usePage().props.activities as Activity[];

    // Activities State
    const [activitiesData, setActivitiesData] = useState({
        id: activitiesTitle?.id ?? 0,
        main_title: activitiesTitle?.main_title ?? "Những Hoạt Động Nổi Bật Tại Nha Khoa 246",
        subtitle: activitiesTitle?.subtitle ?? "Khám phá các hoạt động và dịch vụ nổi bật tại nha khoa của chúng tôi – từ chăm sóc răng miệng tổng quát đến các giải pháp thẩm mỹ hiện đại. Nha Khoa 246 luôn đặt chất lượng điều trị và sự hài lòng của bạn lên hàng đầu.",
        section_name: "activities",
        activities: activities ?? []
    });

    // Activities Functions
    const addActivity = () => {
        setActivitiesData({...activitiesData, 
            activities: [...activitiesData.activities, {
                id: "Id" + Date.now(),
                shorttitle: "Tiêu đề ngắn",
                longtitle: "Tiêu đề đầy đủ",
                short_description: "Tóm tắt về hoạt động",
                long_description: "Nội dung chi tiết về hoạt động",
                image: "",
                type: "image",
                date: "Tháng Một 2025",
                participants: "0+ người tham gia",
                media_url: ""
            }]
        });
    };

    const removeActivity = (index: number) => {
        setActivitiesData({...activitiesData, activities: activitiesData.activities.filter((_, i) => i !== index)});
    };

    const updateActivity = (index: number, field: string, value: any) => {
        const updated = [...activitiesData.activities];
        updated[index] = { ...updated[index], [field]: value };
        setActivitiesData({...activitiesData, activities: updated});
    };

    useEffect(() => {
        if (!activitiesTitle) return;

        setActivitiesData({
            activities: activities ?? [],
            ...activitiesTitle
        });
    }, [activitiesTitle, activities]);

    const handleSave = () => {
        if (activitiesData.id !== 0) {
            router.put(
                route('activities.update', activitiesData.id), 
                activitiesData, 
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
            
        } else {
            router.post(route('activities.store'), 
                activitiesData,
                {
                    preserveScroll: true,
                    onSuccess: () => toast.success("Cập nhật thành công!")
                });
        }
    };
    
    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center">
                        <Calendar className="w-5 h-5 mr-2" />
                        Hoạt động nha khoa
                    </span>
                    {/* <Button onClick={addActivity} size="sm">
                        <Plus className="w-4 h-4" />
                        Thêm hoạt động
                    </Button> */}
                </CardTitle>
                <CardDescription>Quản lý nội dung và hình ảnh của hoạt động nha khoa</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="space-y-4">
                    <div>
                        <Label htmlFor="main_title">Tiêu đề</Label>
                        <Input
                        id="main_title"
                        value={activitiesData.main_title}
                        onChange={(e) => setActivitiesData({...activitiesData, main_title: e.target.value})}
                        />
                    </div>
                    <div>
                        <Label htmlFor="subtitle">Phụ đề</Label>
                        <Textarea
                        id="subtitle"
                        value={activitiesData.subtitle}
                        onChange={(e) => setActivitiesData({...activitiesData, subtitle: e.target.value})}
                        rows={3}
                        />
                    </div>
                </div>

                <Separator />
                
                <div className="space-y-4">
                    <h4 className="font-medium">Danh sách hoạt động</h4>
                    {activitiesData.activities.map((activity, index) => (
                    <Card key={activity.id} className="relative">
                        <CardHeader className="pb-4">
                            <div className="flex items-center justify-between">
                            <Badge>Hoạt động {index + 1}</Badge>
                            <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => removeActivity(index)}
                            >
                                <Trash2 className="w-4 h-4" />
                            </Button>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label>Loại media</Label>
                                <Select value={activity.type} onValueChange={(value) => updateActivity(index, 'type', value)}>
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Loại media" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="image">Hình Ảnh</SelectItem>
                                        <SelectItem value="video">Video</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="grid md:grid-cols-2 gap-4">
                                <div>
                                    <Label>Tiêu đề ngắn</Label>
                                    <Input
                                    value={activity.shorttitle}
                                    onChange={(e) => updateActivity(index, 'shorttitle', e.target.value)}
                                    />
                                </div>
                                <div>
                                    <Label>Tiêu đề đầy đủ</Label>
                                    <Input
                                    value={activity.longtitle}
                                    onChange={(e) => updateActivity(index, 'longtitle', e.target.value)}
                                    />
                                </div>
                            </div>
                            <div>
                                <Label>Mô tả ngắn</Label>
                                <Textarea
                                    value={activity.short_description}
                                    onChange={(e) => updateActivity(index, 'short_description', e.target.value)}
                                    rows={2}
                                />
                            </div>
                            <div>
                                <Label>Nội dung chi tiết</Label>
                                <Textarea
                                    value={activity.long_description}
                                    onChange={(e) => updateActivity(index, 'long_description', e.target.value)}
                                    rows={5}
                                />
                            </div>
                            <div className="grid md:grid-cols-2 gap-4">
                                <div>
                                    <Label>Thời gian</Label>
                                    <Input
                                    value={activity.date}
                                    onChange={(e) => updateActivity(index, 'date', e.target.value)}
                                    />
                                </div>
                                <div>
                                    <Label>Số lượng tham gia</Label>
                                    <Input
                                    value={activity.participants}
                                    onChange={(e) => updateActivity(index, 'participants', e.target.value)}
                                    />
                                </div>
                            </div>
                            <div>
                                <Label>Link ảnh</Label>
                                <Input
                                value={activity.image}
                                onChange={(e) => updateActivity(index, 'image', e.target.value)}
                                placeholder="Link ảnh"
                                />
                            </div>
                            {activity.type === "video" && (
                                <div>
                                    <Label>Link video</Label>
                                    <Input
                                    value={activity.media_url}
                                    onChange={(e) => updateActivity(index, 'media_url', e.target.value)}
                                    placeholder="Link video"
                                    />
                                </div>
                            )}
                            {/* {activity.type === "video" && (
                                <div>
                                    <Label>Video Thumbnail URL</Label>
                                    <Input
                                    value={activity.thumbnailUrl || ""}
                                    onChange={(e) => updateActivity(index, 'thumbnailUrl', e.target.value)}
                                    placeholder="Custom thumbnail for video"
                                    />
                                </div>
                            )} */}
                        </CardContent>
                    </Card>
                    ))}

                    <Button
                        variant="outline"
                        size="sm"
                        onClick={addActivity}
                    >
                        <Plus className="w-4 h-4" />
                        Thêm hoạt động
                    </Button>
                </div>
                
                <div className="flex justify-end">
                    <Button onClick={() => handleSave()}>
                        <Save className="w-4 h-4" />
                        Lưu thay đổi
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}

export default Activities;