<?php

namespace App\Http\Controllers;

use App\Models\SectionTitle;
use App\Models\Team;
use Illuminate\Http\Request;

class TeamController extends Controller
{
    public function store(Request $request)
    {
        SectionTitle::create($request->all());
        $this->handleTeams($request);
        return back();
    }
    public function update(Request $request, SectionTitle $sectionTitle)
    {
        $sectionTitle->update($request->all());
        $this->handleTeams($request);
        return back();
    }
    public function handleTeams(Request $request)
    {
        $teams = $request['people_list'];

        Team::first()
            ? Team::first()->update(['people_list' => $teams])
            : Team::create(['people_list' => $teams]);
    }
}
