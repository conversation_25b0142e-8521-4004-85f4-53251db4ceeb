import { Card, CardContent } from "@/Components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/Components/ui/avatar";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/Components/ui/carousel";
import { Star, Quote, Play } from "lucide-react";
import { useState } from "react";
import MediaModal from "../../MediaModal";
import { Button } from "@/Components/ui/button";
import { Link } from "@inertiajs/react";

const CustomerReview = () => {

    const [selectedMedia, setSelectedMedia] = useState<{
        type: "image" | "video";
        title: string;
        description: string;
        media_url: string;
        thumbnailUrl?: string;
    } | null>(null);

    const customers = [
        {
            id: 1,
            name: "Vy",
            title: "Sinh viên đại học",
            image: "https://images.unsplash.com/photo-1494790108755-2616b332c265?auto=format&fit=crop&q=80",
            testimonial: "Nha Khoa 246 đã là nơi chăm sóc răng miệng đáng tin cậy cho tôi suốt hơn 3 năm. Dịch vụ chuyên nghiệp và phong cách nhẹ nhàng khiến mỗi lần đến khám đều rất thoải mái",
            rating: 5,
            type: "video" as const,
            treatment: "Chăm sóc, vệ sinh răng miệng",
            media_url: "/videos/phong-van-kh/cam-nhan-cua-vy.mp4",    
        },
        {
            id: 2,
            name: "Anh Hảo",
            title: "Việt kiều Mỹ",
            image: "https://images.unsplash.com/photo-1494790108755-2616b332c265?auto=format&fit=crop&q=80",
            testimonial: "Chất lượng điều trị tại Nha Khoa 246 thật sự xuất sắc. Bác sĩ Wong và đội ngũ đã thay đổi hoàn toàn nụ cười của tôi nhờ kỹ thuật chuyên sâu. Tôi hoàn toàn hài lòng với kết quả nhận được.",
            rating: 5,
            type: "video" as const,
            treatment: "Trồng răng trên Implant",
            media_url: "/videos/phong-van-kh/cam-nhan-cua-anh-hao.mp4",    
            // thumbnailUrl: "https://images.unsplash.com/photo-1494790108755-2616b332c265?auto=format&fit=crop&q=80"
        },
        {
            id: 3,
            name: "Cô Dung",
            title: "Việt kiều Mỹ",
            image: "https://images.unsplash.com/photo-1494790108755-2616b332c265?auto=format&fit=crop&q=80",
            testimonial: "Tôi rất thích đến đây! Dịch vụ nha khoa nhi tuyệt vời. Nhân viên luôn tạo ra môi trường vui vẻ và thân thiện.",
            rating: 5,
            type: "video" as const,
            treatment: "Trồng răng trên Implant",
            media_url: "/videos/phong-van-kh/cam-nhan-cua-co-dung.mp4",    
        },
        {
            id: 4,
            name: "Anh Khoa",
            title: "Việt kiều Nauy",
            image: "https://images.unsplash.com/photo-1494790108755-2616b332c265?auto=format&fit=crop&q=80",
            testimonial: "Từ điều trị khẩn cấp đến kiểm tra định kỳ, Nha Khoa 246 luôn đồng hành cùng tôi. Dịch vụ và đội ngũ chuyên nghiệp giúp tôi hoàn toàn yên tâm.",
            rating: 5,
            type: "video" as const,
            treatment: "Trồng răng trên Implant",
            media_url: "/videos/phong-van-kh/cam-nhan-cua-anh-khoa.mp4",    
            // thumbnailUrl: "https://images.unsplash.com/photo-1494790108755-2616b332c265?auto=format&fit=crop&q=80",
        },
        {
            id: 5,
            name: "Cô Kim Cảnh",
            title: "Bán hàng",
            image: "https://images.unsplash.com/photo-1494790108755-2616b332c265?auto=format&fit=crop&q=80",
            testimonial: "Sự tiện lợi nhờ có nhiều cơ sở và chất lượng đồng nhất giữa các chi nhánh khiến tôi luôn chọn Nha Khoa 246. Mỗi lần đến đều vượt trên mong đợi của tôi.",
            rating: 5,
            type: "video" as const,
            treatment: "Trồng răng trên Implant",
            media_url: "/videos/phong-van-kh/cam-nhan-cua-co-kim-canh.mp4",    
        }
    ];

    const handleVideoClick = (customer: typeof customers[0]) => {
        if (customer.type === "video") {
        setSelectedMedia({
            type: "video",
            title: `Cảm nhận của ${customer.name}`,
            description: `${customer.treatment} - ${customer.testimonial}`,
            media_url: customer.media_url,
            // thumbnailUrl: customer.thumbnailUrl
        });
        }
    };

  return (
    <>
        <section className="py-20 bg-gradient-to-br from-emerald-50 to-indigo-50">
        <div className="container mx-auto px-4">
            <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Khách hàng nói gì về chúng tôi
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Khám phá những chia sẻ chân thành từ những khách hàng đã tin tưởng và trải nghiệm dịch vụ tại Nha Khoa 246.
            </p>
            </div>

            <Carousel
            opts={{
                align: "start",
                loop: true,
            }}
            className="w-full max-w-7xl mx-auto"
            >
            <CarouselContent className="-ml-2 md:-ml-4">
                {customers.map((customer) => (
                <CarouselItem key={customer.id} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                    <Card className="h-full bg-white hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 group border-0 shadow-lg">
                    <CardContent className="p-8 h-full flex flex-col">
                        {/* Header with Avatar and Play Button */}
                        <div className="flex items-center mb-6">
                        <div className="relative">
                            <Avatar className="w-16 h-16 ring-4 ring-emerald-100 group-hover:ring-emerald-200 transition-all duration-300">
                            <AvatarImage src={customer.image} alt={customer.name} className="object-cover" />
                            <AvatarFallback className="bg-emerald-100 text-emerald-600 font-semibold">
                                {customer.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                            </Avatar>
                            {/* {customer.type === "video" && (
                            <div className="absolute -bottom-1 -right-1 bg-red-500 rounded-full p-2 shadow-lg group-hover:scale-110 transition-transform duration-300 cursor-pointer">
                                <Play className="w-3 h-3 text-white fill-white" />
                            </div>
                            )} */}
                        </div>
                        <div className="ml-4 flex-1">
                            <h3 className="font-bold text-gray-900 text-lg">{customer.name}</h3>
                            <p className="text-gray-600 text-sm">{customer.title}</p>
                            <p className="text-emerald-600 text-xs font-medium mt-1">{customer.treatment}</p>
                        </div>
                        </div>

                        {/* Rating */}
                        <div className="flex justify-center mb-4">
                        <div className="flex space-x-1">
                            {[...Array(customer.rating)].map((_, i) => (
                            <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                            ))}
                        </div>
                        </div>

                        {/* Testimonial */}
                        <div className="relative flex-1 flex items-center">
                        <Quote className="absolute -top-2 -left-2 w-8 h-8 text-emerald-100 opacity-50" />
                        <blockquote className="text-gray-700 leading-relaxed text-center italic pl-4 pr-4">
                            "{customer.testimonial}"
                        </blockquote>
                        </div>

                        {/* Video Indicator */}
                        {customer.type === "video" && (
                        <div className="mt-6 text-center">
                            <span 
                                className="inline-flex items-center text-sm text-red-600 hover:text-red-700 cursor-pointer font-medium group-hover:scale-105 transition-transform duration-300"
                                onClick={() => handleVideoClick(customer)}
                            >
                            <Play className="w-4 h-4 mr-2 fill-current" />
                            Xem Video
                            </span>
                        </div>
                        )}
                    </CardContent>
                    </Card>
                </CarouselItem>
                ))}
            </CarouselContent>
            <CarouselPrevious className="hidden md:flex -left-12 bg-white hover:bg-emerald-50 border-2 border-emerald-100 hover:border-emerald-200 text-emerald-600" />
            <CarouselNext className="hidden md:flex -right-12 bg-white hover:bg-emerald-50 border-2 border-emerald-100 hover:border-emerald-200 text-emerald-600" />
            </Carousel>

            {/* Bottom Call to Action */}
            <div className="text-center mt-12">
                <p className="text-gray-600 mb-4">Sẵn sàng bắt đầu hành trình thay đổi nụ cười của riêng bạn chưa?</p>
                <Link href={"/contact"}>
                    <Button className="rounded-full hover:shadow-lg hover:-translate-y-1">
                    Đặt lịch tư vấn ngay bây giờ!
                    </Button>
                </Link>
            </div>
        </div>
        </section>

        <MediaModal
            isOpen={!!selectedMedia}
            onClose={() => setSelectedMedia(null)}
            type={selectedMedia?.type || "video"}
            title={selectedMedia?.title || ""}
            description={selectedMedia?.description}
            media_url={selectedMedia?.media_url || ""}
        />
        </>
  );
};

export default CustomerReview;
